image: gitlab.metaleap.com:5050/library/node-maven/node:22.14.0-alpine-git
stages:
  - deploy
variables:
  NODE_OPTIONS: '--openssl-legacy-provider'
  TZ: 'Asia/Shanghai' # 设置时区为北京时间

deploy-test:
  stage: deploy
  script:
    - yarn
    - yarn add @tarojs/binding-linux-x64-musl@3.6.30
    - export NODE_OPTIONS=--max-old-space-size=8192
    - yarn build:h5:test
  only:
    - test
  tags:
    - k8s
  when: on_success

deploy-live:
  stage: deploy
  script:
    - yarn
    - yarn add @tarojs/binding-linux-x64-musl@3.6.30
    - export NODE_OPTIONS=--max-old-space-size=8192
    - yarn build:h5:live
  only:
    - /^mb-users-ai-box_.*$/
  except:
    - branchs
  tags:
    - k8s
  when: manual
