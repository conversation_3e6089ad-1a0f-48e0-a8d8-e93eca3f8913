import Taro from '@tarojs/taro'
import { useEffect } from 'react'
import { useAsyncFn } from 'react-use'
import { aiAvatarToolList } from '@/api/ai-avatar'
import './index.less'
import { SchemaToApp } from '@/utils'

const localMap = [
  {
    url: '/pages/ai-job/index?toolId=1&toolsType=txt2img',
    title: 'AI头像'
  },
  {
    url: '/pages/ai-job/index?toolId=2&toolsType=txt2img',
    title: 'AI场景'
  },
  {
    url: '/pages/ai-job/index?toolId=4&toolsType=pic2dance',
    title: 'AI舞蹈'
  },
  {
    url: '/pages/ai-job/index?toolId=9&toolsType=ai_story',
    title: 'AI绘本'
  },
  {
    url: '/pages/ai-job-detail/index?jobId=1770&toolsType=ai_story&toolId=9&from=ai-job',
    title: 'AI绘本详情'
  },
  {
    url: '/pages/ai-job-share/index?jobId=2&toolsType=ai_story&toolId=6',
    title: 'AI绘本分享'
  }
]

export default function Index() {
  // 获取列表
  const [listState, listFetch] = useAsyncFn(async () => {
    const res = await aiAvatarToolList({})
    if (res.code !== 200) {
      return []
    }
    return res.payload
  }, [])

  useEffect(() => {
    listFetch()
  }, [listFetch])

  const jump = (item) => {
    if (item.jumpLink.includes('miaobiai')) {
      SchemaToApp(item.jumpLink, () => {})
    } else {
      Taro.navigateTo({
        url: `/pages/ai-job/index?toolId=4&toolsType=pic2dance`
      })
    }
  }

  const localJump = (url) => {
    Taro.navigateTo({
      url
    })
  }

  return (
    <div className="w-full max-w-[750px] m-auto select-none">
      <div className="mx-[56px] my-[50px] flex flex-wrap gap-[18px]">
        {localMap.map((item) => (
          <div className="w-[200px] bg-[#4240c9] text-center text-[20px] py-[10px] rounded-full text-white anim_btn shadow-2xl" key={item.title} onClick={() => localJump(item.url)}>
            {item.title}
          </div>
        ))}
      </div>

      <div className="mx-[56px] mt-[24px] inline-grid grid-cols-2 gap-[24px]">
        {listState.value?.map((item, index) => (
          <div key={index} className="flex anim_btn" onClick={() => jump(item)}>
            <img className="w-full" src={item.toolIcon} />
          </div>
        ))}
      </div>
    </div>
  )
}
