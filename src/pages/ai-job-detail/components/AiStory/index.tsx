import delImg from '@/assets/images/ai-box/trashcan.png'
import downloadImg from '@/assets/images/ai-box/download.png'
import shareImg from '@/assets/images/ai-box/share.png'
import failImg from '@/assets/images/ai-box/fail.png'
import approvalFailedImg from '@/assets/images/ai-box/approval-failed.png'
import palyImg from '@/assets/images/ai-dance/play.png'
import againMaxImg from '@/assets/images/ai-box/again-max.png'
import againImg from '@/assets/images/ai-box/again.png'
import again1MaxImg from '@/assets/images/ai-box/again1-max.png'
import again1Img from '@/assets/images/ai-box/again1.png'
import modal2Img from '@/assets/images/ai-box/modal2.png'
import confirmImg from '@/assets/images/ai-box/confirm.png'
import cancelImg from '@/assets/images/ai-box/cancel.png'
import bj1Img from '@/assets/images/ai-box/bj1.png'
import bj2Img from '@/assets/images/ai-box/bj2.png'
import bj3Img from '@/assets/images/ai-box/bj3.png'
import logoImg from '@/assets/images/ai-story/logo.png'
// import storyImg from "@/assets/images/ai-story/story.png";
import Taro, { useDidHide, useDidShow, useRouter } from '@tarojs/taro'
import Spin from '@/components/Spin'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useAsyncFn, useInterval, useToggle } from 'react-use'
import { aiAvatarRemoveById } from '@/api/ai-avatar'
import { SchemaToApp, getUser } from '@/utils'
import Modal, { defAnimate, maskAnimate } from '@/components/Modal'
import ProgressLoading from '@/components/ProgressLoading'
import { AnimatePresence, motion } from 'framer-motion'
import bearSensors from '@/utils/sensor'
import { aiStoryDetail, aiStoryShare } from '@/api/ai-story'
import SwiperCore from '@msb-next/swiper'
import { Swiper, SwiperSlide } from '@msb-next/swiper/react'
import '@msb-next/swiper/swiper-bundle.min.css'
import useGetState from '@/hooks/useGetState'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import { boxConfState } from '@/store/global'
import { useAtomValue } from 'jotai'
import WithDataFetching from '@/components/WithDataFetching'

export interface Share {
  title: string
  text: string
  link: string
  image: string
}

export interface Detail {
  id: string
  uid: string
  ctime: string
  nickName: string | null
  head: any | null
  toolId: string
  toolName: string
  styleId: string
  styleName: string
  descWord: string
  picOutput: string
  picInput: string
  // 生成状态 1 排队中，2 生成中，3 生成成功 ，4 超时失败，5 违规失败
  genStat: 1 | 2 | 3 | 4 | 5
  // 假进度总时长秒数
  ttlSecond: number
  // 假进度已执行秒数
  hadExeSecond: number
  // 轮询间隔
  intervalSecond: number
  share?: Share
}

interface AudioItem {
  id: string
  type: number
  imageUrl: string
  readText: string
  text: string
  audioUrl: string
}

function Index() {
  const user = getUser()
  const router = useRouter()
  const [delModal, setDelModal] = useToggle(false)
  const [play, setPlay] = useToggle(true)
  const conf = useAtomValue(boxConfState)[router.params.toolId || '1']
  const bgAudioRef = useRef<HTMLAudioElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const [audioIndex, setAudioIndex, getAudioIndex] = useGetState(0) // 音频播放下标
  const [audioList, setAudioList, getAudioList] = useGetState<AudioItem[]>([]) // 音频列表
  const [mainSwiper, setMainSwiper] = useState<SwiperCore | null>(null)
  const [picLoad, setPicLoad] = useToggle(true)
  const [bgReady, setBgReady] = useToggle(false)

  useDidShow(() => {
    if (audioRef.current) {
      audioRef.current.muted = false
    }
    if (bgAudioRef.current) {
      bgAudioRef.current.muted = false
    }
  })

  useDidHide(() => {
    if (audioRef.current) {
      const audio = audioRef.current
      audio.muted = true
      setTimeout(() => {
        audio.pause()
      }, 500)
    }
    if (bgAudioRef.current) {
      const bgAudio = bgAudioRef.current
      bgAudio.muted = true
      setTimeout(() => {
        bgAudio.pause()
      }, 500)
    }
  })

  useEffect(() => {
    SchemaToApp('miaobiai://art?type=fun&method=showShareButton&isShow=0', () => {})
    const audio = audioRef.current
    const bgAudio = bgAudioRef.current
    if (audio) {
      audio.addEventListener('play', () => onPlay(true))
      audio.addEventListener('pause', () => onPlay(false))
      requestAnimationFrame(updateProgressBar)
    }
    return () => {
      if (audio) {
        // 清除事件监听器
        audio.removeEventListener('play', onPlay)
        audio.removeEventListener('pause', onPlay)
        audio.pause()
      }
      if (bgAudio) {
        bgAudio.pause()
      }
    }
  }, [])

  // 音频播放完毕自动下标指向下一个
  useEffect(() => {
    audioRef.current!.onended = () => {
      const index = getAudioIndex()
      const list = getAudioList()
      // 最后一个
      if (index === list.length - 1) {
        setAudioIndex(0)
      } else {
        setAudioIndex((v) => v + 1)
      }
    }
    audioRef.current!.oncanplay = () => {
      console.log('oncanplay', audioRef.current)
      audioRef.current?.play().catch(() => {})
    }
    bgAudioRef.current!.oncanplay = () => {
      setBgReady(true)
    }
  }, [])

  // 控制背景音乐的播放
  useEffect(() => {
    if (play) {
      if (bgReady) {
        bgAudioRef.current?.play().catch(() => {})
      }
    } else {
      setTimeout(() => {
        if (getAudioIndex() !== getAudioList().length - 1 && audioRef.current?.paused) {
          bgAudioRef.current?.pause()
        }
      }, 100)
    }
  }, [bgReady, play])

  // 根据下标逐步播放音频
  useEffect(() => {
    if (audioRef.current && audioList.length) {
      console.log('播放', audioIndex, audioList[audioIndex])
      if (audioList.length && audioList[audioIndex] && audioList[audioIndex].audioUrl) {
        audioRef.current.src = audioList[audioIndex].audioUrl
        audioRef.current?.play().catch(() => {})
      } else {
        audioRef.current.pause()
      }
    }
    mainSwiper?.slideTo(audioIndex + 1)
  }, [audioList, audioIndex])

  // 在音频加载完元数据时获取音频时长
  const onPlay = useCallback((isPlay) => {
    setPlay(isPlay)
  }, [])

  // 更新进度条
  const updateProgressBar = () => {
    if (audioRef.current) {
      const progress = (audioRef.current.currentTime / audioRef.current.duration) * 100
      // 设置progressBarRef.current子节点的宽度
      const audioIndexCurrent = getAudioIndex()
      const progressBarList = document.querySelectorAll<HTMLElement>('.progressBar')
      progressBarList.forEach((item, index) => {
        item!.style.width = audioIndexCurrent <= index ? '0%' : '100%'
        if (audioIndexCurrent > index) {
          item!.style.width = `100%`
        } else if (audioIndexCurrent === index) {
          if (audioIndexCurrent === progressBarList.length - 1) {
            item!.style.width = `100%`
          } else {
            item!.style.width = `${progress}%`
          }
        } else {
          item!.style.width = '0%'
        }
      })
      requestAnimationFrame(updateProgressBar)
    }
  }

  // 获取详情
  const [detailState, detailFetch] = useAsyncFn<() => Promise<Detail | null>>(async () => {
    const res = await aiStoryDetail({
      id: router.params.jobId
    })
    if (res.code !== 200) {
      return null
    }
    let share = null
    if (res.payload.workData.genStat === 3) {
      setAudioList(res.payload.storyContent.wikiVo.workData.imageList || [])
      if (bgAudioRef.current) {
        bgAudioRef.current.src = res.payload.storyContent.wikiVo.workData.backgroundMusicUrl
        bgAudioRef.current.loop = true
        bgAudioRef.current.volume = 0.5
        bgAudioRef.current?.play().catch(() => {})
      }
      const shareRes = await aiStoryShare(router.params.jobId)
      share = shareRes.payload
    }
    return { ...res.payload.workData, share }
  }, [])

  // 第一次获取详情
  useEffect(() => {
    detailFetch()
  }, [detailFetch])

  // 轮询详情
  useInterval(
    () => {
      detailFetch()
    },
    detailState.value && (detailState.value?.genStat === 1 || detailState.value?.genStat === 2) ? (detailState.value?.intervalSecond || 0) * 1000 : null
  )

  // 删除
  const [delState, delFetch] = useAsyncFn(async () => {
    bearSensors('xxms_AIbox_works_delete', {
      uid: user.id,
      AIbox_type: detailState.value?.toolName // 类型
    })
    const res = await aiAvatarRemoveById({
      id: router.params.jobId,
      uid: user.id
    })
    console.log(res)
    if (res.code !== 200) {
      return []
    }
    if (router.params.from === 'ai-job') {
      Taro.redirectTo({
        url: `/pages/ai-job/index?&toolsType=${router.params.toolsType}&toolId=${router.params.toolId}`
      })
    } else {
      // 刷新app数据
      SchemaToApp(
        `miaobiai://art?type=fun&method=reloadData&json=${JSON.stringify({
          source: 'AICreateList'
        })}`,
        () => {
          // closewebview
          SchemaToApp('miaobiai://art?type=fun&method=closewebview', () => {})
        }
      )
    }
    return
  }, [detailState])

  // 下载
  const download = () => {
    bearSensors('xxms_AIbox_works_download', {
      uid: user.id,
      AIbox_type: detailState.value?.toolName // 类型
    })
    let json = {
      address: detailState.value?.picOutput,
      isBase64: false,
      videoId: router.params.jobId,
      isPicture: 2
    }
    SchemaToApp('miaobiai://art?type=fun&method=savePhotoToAlbum&json=' + JSON.stringify(json), () => {})
  }

  // 分享
  const share = () => {
    bearSensors('xxms_AIbox_works_share', {
      uid: user.id,
      AIbox_type: detailState.value?.toolName // 类型
    })
    let json = {
      //0是链接、1是图片
      isPicture: 2,
      hasDouyin: 1, //0 不包含抖音，1包含抖音
      channelId: 0,
      sendId: user.id,
      videoId: router.params.jobId,
      title: detailState.value?.share?.title,
      imageUrl: detailState.value?.share?.image,
      link: detailState.value?.share?.link
    }
    SchemaToApp('miaobiai://art?type=fun&method=showSharePosterImage&json=' + JSON.stringify(json), () => {})
  }

  // 继续创作
  const goOn = () => {
    if (router.params.toolsType === 'pic2img') {
      SchemaToApp(`${conf.path}&toolId=${router.params.toolId}&toolName=${detailState.value?.toolName}&isClear=1`, () => {})
    } else {
      const url = `/pages/ai-job/index?toolId=${detailState.value?.toolId}&toolsType=${router.params.toolsType}&jobId=${router.params.jobId}`
      if (router.params.from === 'ai-job') {
        Taro.navigateBack()
      } else {
        Taro.navigateTo({ url })
      }
    }
  }

  return (
    <div className="w-full mx-auto select-none min-h-full flex flex-col" style={{ backgroundColor: conf.bgColor, fontFamily: 'HYZhengYuan-55W' }}>
      <img className="absolute w-[143px] h-[281px] md:w-[11.726vw] md:h-[23.042vw] right-0" src={bj1Img} />
      <img className="absolute w-[257px] h-[212px] top-[612px] md:w-[21.074vw] md:h-[17.384vw] md:top-[50.184vw]" src={bj2Img} />
      <img className="absolute w-[305px] h-[197px] right-0 bottom-[156px] md:w-[25.01vw] md:h-[16.154vw] md:right-0 md:bottom-[12.7vw]" src={bj3Img} />
      {/* 背景 */}
      <audio preload="auto" ref={bgAudioRef} />

      {/* 绘本声音 */}
      <audio preload="auto" ref={audioRef} />

      {/* 展示 */}
      <div className="w-[670px] h-[1261px] py-[35px] md:py-[2.11vw] md:w-[56.61vw] md:h-[105.06vw] m-auto overflow-hidden">
        <div className="relative rounded-[40px] w-full h-full my-auto overflow-hidden" style={{ backgroundColor: conf.darkColor }}>
          {/* 外边框
        {conf.pictureBg ? (
          <img className="w-full h-full absolute z-0" src={conf.pictureBg} />
        ) : null} */}

          <div className="w-full h-full relative flex">
            {/* 正在生成中请稍等 */}
            {((detailState.value?.genStat === 1 || detailState.value?.genStat === 2) && detailState.value.ttlSecond) ||
            (detailState.value?.genStat !== 4 && detailState.value?.genStat !== 5 && detailState.value?.ttlSecond && !picLoad && router.params.from === 'ai-job') ? (
              <ProgressLoading
                defaultProgress={parseInt(`${(detailState.value.hadExeSecond / detailState.value.ttlSecond) * 100}`)}
                time={(detailState.value.ttlSecond - detailState.value.hadExeSecond) * 1000}
                txt={<span className="text-white">正在生成中请稍等</span>}
              />
            ) : null}

            {/* 正常 */}
            {detailState.value?.genStat === 3 ? (
              <>
                <Swiper
                  loop
                  className="w-full"
                  onSwiper={(e) => setMainSwiper(e)}
                  onSlideChange={(swiper) => {
                    setAudioIndex(swiper.realIndex)
                  }}
                >
                  {audioList.map((item, index) => (
                    <SwiperSlide key={index}>
                      <div
                        className="w-full h-full flex_center"
                        onClick={() => {
                          audioRef.current?.pause()
                        }}
                      >
                        <ImgPlaceholder className="w-full h-full object-cover" src={item.imageUrl} />
                        {index === 0 ? (
                          <div className="mt-[241px] md:mt-[20.5vw] w-full absolute top-0 flex flex-col items-center">
                            <div className="w-full h-[67px] md:h-[5.67vw] flex justify-end">
                              <img className="w-[357px] h-[67px] mr-[50px] md:w-[30.03vw] md:h-[5.67vw] md:mr-[5vw]" src={conf.tag} alt="" />
                            </div>
                            <div
                              className="mx-[50px] mt-[10px] text-[77px] md:mx-[5.2vw] md:mt-[0.82vw] md:text-[6.44vw] text-white leading-[100px] text-center not-italic normal-case relative z-0 ai_story_stroke"
                              data-content={detailState.value?.descWord}
                            >
                              {detailState.value?.descWord}
                            </div>
                          </div>
                        ) : index === audioList.length - 1 ? (
                          <div className="mt-[153px] md:mt-[8.66vw] w-full absolute top-0 flex flex-col items-center">
                            <img className="w-[325px] h-[57px] md:w-[27.15vw] md:h-[4.62vw]" src={logoImg} alt="" />
                            <div className="mt-[112px] text-[40px] leading-[22px] md:mt-[9.38vw] md:text-[3.36vw] md:leading-[1.82vw] text-white font-semibold text-center not-italic normal-case">
                              《{detailState.value?.descWord}》绘本
                            </div>
                            <div className="mt-[27px] leading-[22px] text-[24px] md:mt-[2.28vw] md:leading-[1.82vw] md:text-[2.02vw] font-semibold text-white  text-center not-italic normal-case">
                              作者：{detailState.value?.nickName}
                            </div>
                          </div>
                        ) : (
                          <div className="absolute bottom-[118px] mx-auto w-[550px] p-[30px] text-[26px] rounded-[13px] leading-[41px] md:bottom-[10.35vw] md:mx-auto md:w-[46.97vw] md:p-[2.41vw] md:text-[2.132vw] md:rounded-[1.066vw] md:leading-[3.362vw] bg-[#00000035] font-normal text-white not-italic normal-case">
                            {item.text}
                            <img className="absolute w-[322px] h-[61px] top-[-61px] md:w-[27.04vw] md:h-[5.12vw] md:top-[-5.12vw] right-0" src={conf.tag} alt="" />
                          </div>
                        )}
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                <AnimatePresence>
                  {!play && audioList[audioIndex].audioUrl ? (
                    <motion.div {...maskAnimate} className="absolute z-10 w-full h-full flex_center pointer-events-none">
                      <motion.img
                        {...defAnimate}
                        onClick={() => {
                          audioRef.current?.play().catch(() => {})
                        }}
                        className="w-[151px] h-[152px] md:w-[12.76vw] md:h-[12.76vw] pointer-events-auto"
                        src={palyImg}
                      />
                    </motion.div>
                  ) : null}
                </AnimatePresence>

                <div className="absolute z-10 bottom-[85px] left-[30px] w-[610px] h-[8px] gap-[20px] md:bottom-[7.68vw] md:left-[2.41vw] md:w-[51.79vw] md:h-[0.4vw] md:gap-[1.64vw] flex justify-between items-center">
                  {audioList.map((item, index) => (
                    <div key={index} className="flex-1 h-full rounded-full overflow-hidden bg-black">
                      <div id={`progressBar_${index}`} className="progressBar bg-white h-full"></div>
                    </div>
                  ))}
                </div>
              </>
            ) : null}

            {/* 生成失败 */}
            {detailState.value?.genStat === 4 ? (
              <div className="w-full h-full flex_center flex-col">
                <img className="w-[189px] h-[137px] mb-[46px] md:w-[15.498vw] md:h-[11.234vw] md:mb-[3.772vw]" src={failImg} />
                <div className="font-normal text-[30px] md:text-[2.46vw] text-[#3C607B]">生成失败，请重新尝试</div>
              </div>
            ) : null}

            {/* 图片审核不通过 */}
            {detailState.value?.genStat === 5 ? (
              <div className="w-full h-full flex_center flex-col">
                <img className="w-[244px] h-[202px] mb-[46px] md:w-[20.008vw] md:h-[16.564vw] md:mb-[3.772vw]" src={approvalFailedImg} />
                <div className="flex_center flex-col font-normal text-[#3C607B] text-[31px] leading-[48px] md:text-[2.542vw] md:leading-[3.936vw]">
                  <div className="text-center">图片审核不通过</div>
                  <div className="text-center">请调整后，再次尝试</div>
                </div>
              </div>
            ) : null}
          </div>
        </div>
      </div>

      {router.params.preview !== '1' && <div className="w-full mx-auto h-[139px] md:h-[11.4vw]"></div>}
      {/* 操作 */}
      {router.params.preview !== '1' && <div
        className="w-full mx-auto h-[139px] md:h-[11.4vw] flex_center fixed z-50 bottom-0"
        style={{
          backgroundColor: conf.bgColor
        }}
      >
        <div className="w-[674px] md:w-full mx-auto md:mx-[2.73vw] flex items-center justify-between">
          {detailState.value?.genStat !== 1 && detailState.value?.genStat !== 2 ? (
            <Spin bgColor="transparent" state={delState.loading} onClick={setDelModal} className="w-[99px] h-[99px] md:w-[8.4vw] md:h-[8.4vw] flex_center rounded-full anim_btn">
              <img className="w-[99px] h-[99px] md:w-[8.4vw] md:h-[8.4vw]" src={delImg} />
            </Spin>
          ) : null}
          {detailState.value?.genStat === 3 ? (
            <>
              {/* <img onClick={download} className="w-[99px] h-[99px] md:w-[8.4vw] md:h-[8.4vw] flex_center rounded-full anim_btn" src={downloadImg}></img>
              <img onClick={share} className="w-[99px] h-[99px] md:w-[8.4vw] md:h-[8.4vw] flex_center rounded-full anim_btn" src={shareImg}></img> */}
            </>
          ) : null}

          {detailState.value?.genStat !== 5 && router.params.from === 'ai-job' ? (
            <>
              <img
                onClick={goOn}
                className={`flex_center md:hidden relative anim_btn h-[101px]
              ${detailState.value?.genStat === 1 || detailState.value?.genStat === 2 ? 'flex-1' : 'w-[313px]'}
              `}
                src={detailState.value?.genStat === 1 || detailState.value?.genStat === 2 ? againMaxImg : againImg}
              ></img>
              <img
                onClick={goOn}
                className={`hidden md:flex_center relative anim_btn h-[8.72vw]
              ${detailState.value?.genStat === 1 || detailState.value?.genStat === 2 ? 'flex-1' : 'w-[62.5vw]'}
              `}
                src={detailState.value?.genStat === 1 || detailState.value?.genStat === 2 ? again1MaxImg : again1Img}
              ></img>
            </>
          ) : null}
        </div>
      </div>}

      <Modal show={delModal}>
        <div className="w-[626px] h-[738px] md:w-[51.37vw] md:h-[44.83vw]">
          <div className="w-full h-[439px] md:h-[36vw] relative flex_center">
            <img src={modal2Img} className="absolute z-0 w-full h-full"></img>
            <div className="relative font-normal text-[31px] mt-[20px] md:text-[2.52vw] md:mt-[1.6258vw] text-[#D95200] leading-[1] text-center">你确定要删除此作品吗？</div>
          </div>

          <div className="w-[532px] h-[101px] mt-[11px] md:w-[43.45vw] md:h-[8.33vw] md:mt-[0.5vw] mx-auto flex justify-between items-center">
            <div onClick={setDelModal} className="anim_btn w-[254px] h-[101px] md:w-[20.8vw] md:h-[8.33vw]">
              <img src={cancelImg} className="w-full h-full"></img>
            </div>
            <div onClick={delFetch} className="anim_btn w-[254px] h-[101px] md:w-[20.8vw] md:h-[8.33vw]">
              <img src={confirmImg} className="w-full h-full"></img>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default WithDataFetching(Index)
