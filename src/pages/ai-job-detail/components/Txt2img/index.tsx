import copyImg from '@/assets/images/ai-box/copy.png'
import delImg from '@/assets/images/ai-box/trashcan.png'
import downloadImg from '@/assets/images/ai-box/download.png'
import shareImg from '@/assets/images/ai-box/share.png'
import failImg from '@/assets/images/ai-box/fail.png'
import approvalFailedImg from '@/assets/images/ai-box/approval-failed.png'
import palyImg from '@/assets/images/ai-dance/play.png'
import againMaxImg from '@/assets/images/ai-box/again-max.png'
import againImg from '@/assets/images/ai-box/again.png'
import again1MaxImg from '@/assets/images/ai-box/again1-max.png'
import again1Img from '@/assets/images/ai-box/again1.png'
import modal2Img from '@/assets/images/ai-box/modal2.png'
import confirmImg from '@/assets/images/ai-box/confirm.png'
import cancelImg from '@/assets/images/ai-box/cancel.png'
import headImg from '@/assets/images/ai-box/head.png'
import bj1Img from '@/assets/images/ai-box/bj1.png'
import bj2Img from '@/assets/images/ai-box/bj2.png'
import bj3Img from '@/assets/images/ai-box/bj3.png'
import Taro, { useDidHide, useDidShow, useRouter } from '@tarojs/taro'
import Spin from '@/components/Spin'
import { useEffect, useRef } from 'react'
import { useAsyncFn, useCopyToClipboard, useInterval, useToggle } from 'react-use'
import { aiAvatarDetail, aiAvatarDetailNoToken, aiAvatarRemoveById } from '@/api/ai-avatar'
import { SchemaToApp, getUser, versionInfo } from '@/utils'
import Modal, { defAnimate, maskAnimate } from '@/components/Modal'
import ProgressLoading from '@/components/ProgressLoading'
import { AnimatePresence, motion } from 'framer-motion'
import bearSensors from '@/utils/sensor'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import { boxConfState } from '@/store/global'
import { useAtomValue } from 'jotai'
import WithDataFetching from '@/components/WithDataFetching'

export interface Detail {
  id: string
  uid: string
  ctime: string
  nickName: string | null
  head: any | null
  toolId: string
  toolName: string
  styleId: string
  styleName: string
  descWord: string
  picOutput: string
  picInput: string
  // 生成状态 1 排队中，2 生成中，3 生成成功 ，4 超时失败，5 违规失败
  genStat: number
  // 假进度总时长秒数
  ttlSecond: number
  // 假进度已执行秒数
  hadExeSecond: number
  // 轮询间隔
  intervalSecond: number
}

function Index() {
  const user = getUser()
  const router = useRouter()
  const [delModal, setDelModal] = useToggle(false)
  const [picLoad, setPicLoad] = useToggle(false)
  const [_, copyToClipboard] = useCopyToClipboard()
  const [play, setPlay] = useToggle(true)
  const videoRef = useRef<HTMLVideoElement>(null)
  const conf = useAtomValue(boxConfState)[router.params.toolId || '1']

  useEffect(() => {
    SchemaToApp('miaobiai://art?type=fun&method=showShareButton&isShow=0', () => {})
    return () => {
      if (videoRef.current) {
        videoRef.current.pause()
      }
    }
  }, [])

  useDidShow(() => {
    if (videoRef.current) {
      videoRef.current.muted = false
      videoRef.current.play()
    }
  })

  useDidHide(() => {
    if (videoRef.current) {
      const video = videoRef.current
      video.muted = true
      setTimeout(() => {
        video.pause()
      }, 500)
    }
  })

  useEffect(() => {
    if (videoRef.current) {
      if (play) {
        videoRef.current.play()
      } else {
        videoRef.current.pause()
      }
    }
  }, [play])

  // 获取详情
  const [detailState, detailFetch] = useAsyncFn<() => Promise<Detail | null>>(async () => {
    // {{ AURA-X: Modify - 根据preview参数决定调用哪个API. }}
    const isPreview = router.params.preview === '1'
    const res = isPreview
      ? await aiAvatarDetailNoToken({
          id: router.params.jobId
        })
      : await aiAvatarDetail({
          id: router.params.jobId,
          uid: user.id
        })
    if (res.code !== 200) {
      return null
    }
    return res.payload
  }, [])

  // 第一次获取详情
  useEffect(() => {
    detailFetch()
  }, [detailFetch])

  // 轮询详情
  useInterval(
    () => {
      detailFetch()
    },
    detailState.value && (detailState.value?.genStat === 1 || detailState.value?.genStat === 2) ? (detailState.value?.intervalSecond || 0) * 1000 : null
  )

  // 删除
  const [delState, delFetch] = useAsyncFn(async () => {
    bearSensors('xxms_AIbox_works_delete', {
      uid: user.id,
      AIbox_type: detailState.value?.toolName // 类型
    })
    const res = await aiAvatarRemoveById({
      id: router.params.jobId,
      uid: user.id
    })
    console.log(res)
    if (res.code !== 200) {
      return []
    }
    if (router.params.from === 'ai-job') {
      Taro.redirectTo({
        url: `/pages/ai-job/index?&toolsType=${router.params.toolsType}&toolId=${router.params.toolId}`
      })
    } else {
      // 刷新app数据
      SchemaToApp(
        `miaobiai://art?type=fun&method=reloadData&json=${JSON.stringify({
          source: 'AICreateList'
        })}`,
        () => {
          // closewebview
          SchemaToApp('miaobiai://art?type=fun&method=closewebview', () => {})
        }
      )
    }
    return
  }, [detailState])

  // 复制
  const copy = () => {
    Taro.showToast({ title: '复制成功', icon: 'none' })
    copyToClipboard(detailState.value!.descWord)
  }

  // 下载
  const download = () => {
    bearSensors('xxms_AIbox_works_download', {
      uid: user.id,
      AIbox_type: detailState.value?.toolName // 类型
    })
    let json = {
      address: detailState.value?.picOutput,
      isBase64: false,
      isPicture: router.params.toolsType === 'pic2dance' ? 2 : 1
    }
    SchemaToApp('miaobiai://art?type=fun&method=savePhotoToAlbum&json=' + JSON.stringify(json), () => {})
  }

  // 分享
  const share = () => {
    bearSensors('xxms_AIbox_works_share', {
      uid: user.id,
      AIbox_type: detailState.value?.toolName // 类型
    })
    // 4.5.5版本以上
    // if (versionInfo.versionNumber > 40505) {
    let json = {
      //0是链接、1是图片
      isPicture: router.params.toolsType === 'pic2dance' ? 2 : 1,
      hasDouyin: 1, //0 不包含抖音，1包含抖音
      channelId: 0,
      sendId: user.id,
      link: detailState.value?.picOutput
    }
    SchemaToApp('miaobiai://art?type=fun&method=showSharePosterImage&json=' + JSON.stringify(json), () => {})
    // } else {
    //   let json = {
    //     //0是链接、1是图片
    //     isPicture: 1,
    //     hasDouyin: 1, //0 不包含抖音，1包含抖音
    //     channelId: 111,
    //     sendId: 111,
    //     link: detailState.value?.picOutput
    //   }
    //   SchemaToApp('miaobiai://art?type=fun&method=showSharePoste&json=' + JSON.stringify(json), () => {})
    // }
  }

  // 继续创作
  const goOn = () => {
    if (router.params.toolsType === 'pic2img') {
      SchemaToApp(`${conf.path}&toolId=${router.params.toolId}&toolName=${detailState.value?.toolName}&isClear=1`, () => {})
    } else {
      const url = `/pages/ai-job/index?toolId=${detailState.value?.toolId}&toolsType=${router.params.toolsType}&jobId=${router.params.jobId}`
      if (router.params.from === 'ai-job') {
        Taro.navigateBack()
      } else {
        Taro.navigateTo({ url })
      }
    }
  }

  return (
    <div className="w-full h-full" style={{ backgroundColor: conf.bgColor, fontFamily: 'HYZhengYuan-55W' }}>
      {/* 背景 */}
      <img className="absolute w-[143px] h-[281px] md:w-[11.726vw] md:h-[23.042vw] right-0" src={bj1Img} />
      <img className="absolute w-[257px] h-[212px] top-[612px] md:w-[21.074vw] md:h-[17.384vw] md:top-[50.184vw]" src={bj2Img} />
      <img className="absolute w-[305px] h-[197px] right-0 bottom-[164px] md:w-[25.01vw] md:h-[16.154vw] md:right-0 md:bottom-[11.4vw]" src={bj3Img} />

      <div className="w-full h-full flex flex-col relative">
        <div className="flex-1 w-full overflow-auto pt-[38px] md:pt-[2.69vw]">
          {/* 展示 */}
          <div
            className={`relative ${
              router.params.toolsType === 'pic2dance'
                ? // 1333
                  'rounded-[20px] w-[674px] h-[1200px] md:rounded-[1.95vw] md:w-[56.61vw] md:h-[100.84vw] mx-auto overflow-hidden bg-white'
                : 'rounded-[87px] w-[674px] h-[685px] md:rounded-[7.81vw] md:w-[47.41vw] md:h-[48.184vw] mx-auto overflow-hidden'
            }`}
          >
            {/* 外边框 */}
            {conf.pictureBg ? <img className="w-full h-full absolute z-0" src={conf.pictureBg} /> : null}

            <div className="w-full h-full relative flex">
              {/* 正在生成中请稍等 */}
              {((detailState.value?.genStat === 1 || detailState.value?.genStat === 2) && detailState.value.ttlSecond) ||
              (detailState.value?.genStat !== 4 && detailState.value?.genStat !== 5 && detailState.value?.ttlSecond && !picLoad && router.params.from === 'ai-job') ? (
                <ProgressLoading
                  defaultProgress={parseInt(`${(detailState.value.hadExeSecond / detailState.value.ttlSecond) * 100}`)}
                  time={(detailState.value.ttlSecond - detailState.value.hadExeSecond) * 1000}
                  txt={
                    router.params.toolsType === 'pic2dance' ? (
                      <div className="leading-[46px] md:leading-[3.8vw]">
                        <p>生成中，请耐心等待一下下</p>
                        <p>生成完毕小伴熊会告诉你的呦</p>
                      </div>
                    ) : (
                      '正在生成中请稍等'
                    )
                  }
                />
              ) : null}

              {/* 正常 */}
              {detailState.value?.genStat === 3 ? (
                router.params.toolsType === 'pic2dance' ? (
                  <>
                    <video
                      ref={videoRef}
                      onClick={setPlay}
                      onCanPlay={() => {
                        setPicLoad(true)
                        videoRef.current?.play().catch((e) => {
                          console.log(e)
                          setPlay(false)
                        })
                      }}
                      autoPlay
                      className="w-full h-full mx-auto"
                      webkit-playsinline="true"
                      playsInline
                      loop
                      src={detailState.value?.picOutput}
                    />
                    <AnimatePresence>
                      {!play ? (
                        <motion.div {...maskAnimate} className="absolute w-full h-full bg-[#00000045] flex_center">
                          <motion.img {...defAnimate} onClick={setPlay} className="w-[151px] h-[152px] md:w-[12.76vw] md:h-[12.76vw]" src={palyImg} />
                        </motion.div>
                      ) : null}
                    </AnimatePresence>
                  </>
                ) : (
                  <ImgPlaceholder
                    onLoad={() => setPicLoad(true)}
                    className="w-[674px] h-[674px] md:w-[47.41vw] md:h-[47.41vw] object-contain rounded-[80px] md:rounded-[5.63vw]"
                    src={detailState.value?.picOutput}
                  />
                )
              ) : null}

              {/* 生成失败 */}
              {detailState.value?.genStat === 4 ? (
                <div className="w-full h-full flex_center flex-col">
                  <img className="w-[189px] h-[137px] mb-[46px] md:w-[15.63vw] md:h-[11.33vw] md:mb-[3.8vw]" src={failImg} />
                  <div className="font-normal text-[30px] md:text-[2.52vw] text-[#3C607B]">生成失败，请重新尝试</div>
                </div>
              ) : null}

              {/* 图片审核不通过 */}
              {detailState.value?.genStat === 5 ? (
                <div className="w-full h-full flex_center flex-col">
                  <img className="w-[244px] h-[202px] mb-[46px] md:w-[13.41vw] md:h-[11.26vw] md:mb-[3.8vw]" src={approvalFailedImg} />
                  <div className="flex_center flex-col font-normal text-[31px] md:text-[2.52vw] text-[#3C607B] leading-[48px] md:leading-[4.032vw]">
                    <div className="text-center">图片审核不通过</div>
                    <div className="text-center">请调整后，再次尝试</div>
                  </div>
                </div>
              ) : null}
            </div>
          </div>

          {/* 用户信息 */}
          <div className="h-[107px] w-[674px] mt-[32px] mb-[43px] md:h-[9.25vw] md:w-[93.55vw] md:mt-[2.41vw] md:mb-[2.67vw] mx-auto flex justify-between items-center">
            <div className="w-[113px] h-[113px] md:w-[9.25vw] md:h-[9.25vw] flex_center relative">
              <img className="w-full h-full absolute z-0" src={headImg} alt="" />
              <img className="relative w-[104px] h-[104px] md:w-[8.4vw] md:h-[8.4vw] rounded-full" src={detailState.value?.head} />
            </div>

            <div className="h-full ml-[30px] md:ml-[2.21vw] flex-1 flex flex-col justify-center items-start">
              <div className="font-medium text-[31px] md:text-[2.51vw] leading-none text-black">{detailState.value?.nickName}</div>
              {/* <div className="font-normal text-[24px] leading-none text-black">
            {dayjs(Number(detailState.value?.ctime)).format(
              "YYYYMMDD HH:mm:ss"
            )}
          </div> */}
            </div>

            <div className="w-[188px] md:w-[14vw] h-full relative flex_center">
              <img className="w-[188px] h-[80px] mt-[15px] md:w-[14vw] md:h-[5.922vw] md:mt-[0.68vw]" src={conf.tag} />
              {/* <div className="absolute left-0 top-0 z-10 w-[152px] h-[62px] flex_center font-[bold] text-[24px] text-[#8D5EC7]">
            {detailState.value?.toolName || "未知工具"}
          </div> */}
            </div>
          </div>

          {/* 文生图、图生图参考图 */}
          {router.params.toolsType === 'txt2img' ? (
            <>
              <div className="font-normal text-black text-[33px] my-[28px] w-[674px] md:text-[2.74vw] md:my-[1.97vw] md:w-[93.55vw] mx-auto">参考图：</div>

              <div className="w-[674px] md:w-[93.55vw] mx-auto">
                <img className="w-[144px] h-[144px] rounded-[20px] md:w-[12.3vw] md:h-[12.3vw] md:rounded-[1.382vw] object-cover" src={detailState.value?.picInput} />
              </div>
            </>
          ) : null}

          {/* 舞蹈参考图 */}
          {router.params.toolsType === 'pic2dance' ? (
            <>
              <div className="font-normal text-black text-[33px] my-[28px] w-[674px] md:text-[2.74vw] md:my-[1.97vw] md:w-[93.55vw] mx-auto">参考图：</div>

              <div className="w-[674px] md:w-[93.55vw] mx-auto">
                <img className="w-[178px] min-h-[238px] rounded-[20px] md:w-[12.3vw] md:min-h-[12.3vw] md:rounded-[1.382vw] object-cover shadow-md" src={detailState.value?.picInput} />
              </div>
            </>
          ) : null}

          {/* 描述词： */}
          {conf.isShowDescWord ? (
            <>
              <div className="font-normal text-black text-[33px] my-[28px] w-[674px] md:text-[2.74vw] md:my-[1.97vw] md:w-[93.55vw] mx-auto">描述词：{!detailState.value?.descWord ? '空' : ''}</div>

              {detailState.value?.descWord ? (
                <div className="rounded-[40px] w-[674px] pb-[80px] md:rounded-[3.91vw] md:w-[93.55vw] md:pb-[5.25vw] min-h-[20.11vw] mx-auto relative" style={{ backgroundColor: conf.darkColor }}>
                  <div className="p-[26px] md:p-[2.34vw] flex flex-wrap">
                    {detailState.value?.descWord.split(',').map((item, index) => (
                      <div key={index} className="min-h-[44px] rounded-[22px] mr-[15px] mb-[15px] md:min-h-[3.11vw] md:rounded-[1.81vw] md:mr-[1.23vw] md:mb-[1.23vw] bg-[#F7F9FF] inline-block">
                        <div className="w-full h-full flex_center">
                          <div className="font-normal text-[24px] mx-[20px] md:text-[1.97vw] md:ml-[1.64vw]" style={{ color: conf.darkColor }}>
                            {item}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div
                    onClick={copy}
                    className="absolute bottom-[15px] right-[15px] w-[87px] h-[88px] md:bottom-[0.72vw] md:right-[0.72vw] md:w-[7.36vw] md:h-[7.29vw] rounded-full flex_center anim_btn"
                  >
                    <img className="w-full h-full" src={copyImg} />
                  </div>
                </div>
              ) : null}
            </>
          ) : null}

          {router.params.toolsType === 'txt2img' ? <div className="font-normal text-black text-[33px] my-[28px] w-[674px] md:text-[2.74vw] md:my-[1.97vw] md:w-[93.55vw] mx-auto">风格：</div> : null}
          {router.params.toolsType === 'pic2img' ? <div className="font-normal text-black text-[33px] my-[28px] w-[674px] md:text-[2.74vw] md:my-[1.97vw] md:w-[93.55vw] mx-auto">模型：</div> : null}
          {router.params.toolsType === 'pic2dance' ? (
            <div className="font-normal text-black text-[33px] my-[28px] w-[674px] md:text-[2.74vw] md:my-[1.97vw] md:w-[93.55vw] mx-auto">舞蹈：{detailState.value?.styleName}</div>
          ) : null}

          {router.params.toolsType !== 'pic2dance' ? (
            <div className="w-[674px] h-[73px] md:w-[93.55vw] md:h-[6.01vw] mx-auto text-[0]">
              <div
                className="h-[73px] px-[70px] rounded-[20px] leading-[73px] text-[33px] md:h-[6.01vw] md:px-[5.66vw] md:rounded-[1.95vw] md:leading-[6.01vw] md:text-[2.73vw] inline-block mx-auto font-normal text-[#FFFFFF]"
                style={{ backgroundColor: conf.darkColor }}
              >
                {detailState.value?.styleName}
              </div>
            </div>
          ) : null}
        </div>
        {/* 操作 */}
        {/* {{ AURA-X: Add - 当preview=1时隐藏底部操作栏. }} */}
        {router.params.preview !== '1' && (
          <div
            className="w-full mx-auto h-[164px] md:h-[11.4vw] z-50 bottom-0 flex_center"
            style={{
              backgroundColor: conf.bgColor
            }}
          >
            <div className="w-[674px] mx-auto md:w-full md:mx-[2.73vw] flex items-center justify-between">
              {detailState.value?.genStat !== 1 && detailState.value?.genStat !== 2 ? (
                <Spin bgColor="transparent" state={delState.loading} onClick={setDelModal} className="w-[99px] h-[99px] md:w-[8.4vw] md:h-[8.4vw] flex_center rounded-full anim_btn">
                  <img className="w-[99px] h-[99px] md:w-[8.4vw] md:h-[8.4vw]" src={delImg} />
                </Spin>
              ) : null}
              {detailState.value?.genStat === 3 ? (
                <>
                  {/* <img onClick={download} className="w-[99px] h-[99px] md:w-[8.4vw] md:h-[8.4vw] flex_center rounded-full anim_btn" src={downloadImg}></img>
                  <img onClick={share} className="w-[99px] h-[99px] md:w-[8.4vw] md:h-[8.4vw] flex_center rounded-full anim_btn" src={shareImg}></img> */}
                </>
              ) : null}

              {detailState.value?.genStat !== 5 ? (
                <>
                  <img
                    onClick={goOn}
                    className={`flex_center md:hidden relative anim_btn h-[101px]
                ${detailState.value?.genStat === 1 || detailState.value?.genStat === 2 ? 'flex-1' : 'w-[313px]'}
                `}
                    src={detailState.value?.genStat === 1 || detailState.value?.genStat === 2 ? againMaxImg : againImg}
                  ></img>
                  <img
                    onClick={goOn}
                    className={`hidden md:flex_center relative anim_btn h-[8.72vw]
                ${detailState.value?.genStat === 1 || detailState.value?.genStat === 2 ? 'flex-1' : 'w-[62.5vw]'}
                `}
                    src={detailState.value?.genStat === 1 || detailState.value?.genStat === 2 ? again1MaxImg : again1Img}
                  ></img>
                </>
              ) : null}
            </div>
          </div>
        )}
      </div>

      <Modal show={delModal}>
        <div className="w-[626px] h-[738px] md:w-[51.37vw] md:h-[44.83vw]">
          <div className="w-full h-[439px] md:h-[36vw] relative flex_center">
            <img src={modal2Img} className="absolute z-0 w-full h-full"></img>
            <div className="relative font-normal text-[31px] mt-[20px] md:text-[2.52vw] md:mt-[1.6258vw] text-[#D95200] leading-[1] text-center">你确定要删除此作品吗？</div>
          </div>

          <div className="w-[532px] h-[101px] mt-[11px] md:w-[43.45vw] md:h-[8.33vw] md:mt-[0.5vw] mx-auto flex justify-between items-center">
            <div onClick={setDelModal} className="anim_btn w-[254px] h-[101px] md:w-[20.8vw] md:h-[8.33vw]">
              <img src={cancelImg} className="w-full h-full"></img>
            </div>
            <div onClick={delFetch} className="anim_btn w-[254px] h-[101px] md:w-[20.8vw] md:h-[8.33vw]">
              <img src={confirmImg} className="w-full h-full"></img>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default WithDataFetching(Index)
