import Taro, { eventCenter, useDidShow, useRouter } from '@tarojs/taro'
import SwiperCore, { Thumbs } from '@msb-next/swiper'
import { Swiper, SwiperSlide } from '@msb-next/swiper/react'
import '@msb-next/swiper/swiper-bundle.min.css'
import './index.less'
import { useEffect, useRef, useState } from 'react'
import { aiDancePrepareList, aiDanceSubmit, aiDanceRemovePic, aiDanceUploadPic } from '@/api/ai-dance'
import Spin from '@/components/Spin'
import { useAsyncFn, useToggle } from 'react-use'
import MaskLoading from '@/components/MaskLoading'
import { SchemaToApp, getUser, isApp, isH5 } from '@/utils'
import add from '@/assets/images/ai-box/add.png'
import removeImg from '@/assets/images/ai-dance/card-close.png'
import closeImg from '@/assets/images/ai-dance/close.png'
import model1Img from '@/assets/images/ai-dance/model1.png'
import suggestion1Img from '@/assets/images/ai-dance/suggestion1.png'
import suggestion2Img from '@/assets/images/ai-dance/suggestion2.png'
import suggestion1MaxImg from '@/assets/images/ai-dance/suggestion1-max.png'
import suggestion2MaxImg from '@/assets/images/ai-dance/suggestion2-max.png'
import generatedGreyImg from '@/assets/images/ai-box/generated-grey.png'
import generated from '@/assets/images/ai-box/generated.png'
import generatedMax from '@/assets/images/ai-box/generated-max.png'
import generatedGreyImgImg from '@/assets/images/ai-box/generated-grey-max.png'
import uploadImg from '@/assets/images/ai-dance/upload.png'
import uploadMaxImg from '@/assets/images/ai-dance/upload-max.png'
import modal2Img from '@/assets/images/ai-box/modal2.png'
import confirmImg from '@/assets/images/ai-box/confirm.png'
import cancelImg from '@/assets/images/ai-box/cancel.png'
import dayjs from 'dayjs'
import ProgressLoading from '@/components/ProgressLoading'
import Modal from '@/components/Modal'
import { useAtomValue } from 'jotai'
import { boxConfState } from '@/store/global'
import WithDataFetching from '@/components/WithDataFetching'

SwiperCore.use([Thumbs])

function Index() {
  const user = getUser()
  const router = useRouter()
  // const conf = boxConf[router.params.toolId || "1"]; // 配置
  const conf = useAtomValue(boxConfState)[router.params.toolId || '1']
  const fileInput = useRef<HTMLInputElement>(null)
  const [delModal, setDelModal] = useToggle(false)
  const [upModal, setUpModal] = useToggle(false)
  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperCore | null>(null)
  const [mainSwiper, setMainSwiper] = useState<SwiperCore | null>(null)
  const [styleData, setStyleData] = useState<{
    toolName: string
    styleId: string
    styleName: string
  } | null>(null)
  const [base64Info, setBase64Info] = useState<{
    base64: string
    picType: number
    timestamp: number
  }>({
    base64: '',
    picType: 0,
    timestamp: 0
  })
  const [activeUrl, setActiveUrl] = useState<string>('')

  // 获取详情
  const [prepareListState, prepareListFetch] = useAsyncFn(async () => {
    const res = await aiDancePrepareList({
      uid: user.id,
      modelType: router.params.modelType
    })
    if (res.code !== 200) {
      return null
    }
    setStyleData(res.payload)
    return res.payload.list
  }, [])

  // 初始化
  useEffect(() => {
    Taro.setNavigationBarTitle({
      title: 'AI舞蹈'
    })
    // 获取列表
    prepareListFetch()
    return () => {}
  }, [])

  // 上传图片
  const [chooseImgState, chooseImgFetch] = useAsyncFn(
    async (base64, picType) => {
      const res = await aiDanceUploadPic({
        base64: base64.split('base64,')[1],
        picType,
        modelType: router.params.modelType,
        uid: user.id
      })
      if (res.code !== 200) {
        return ''
      }
      prepareListFetch().then(() => {
        setTimeout(() => {
          mainSwiper?.slideTo(1, 0)
        }, 200)
      })
      return res.payload
    },
    [mainSwiper]
  )

  // 图片
  useEffect(() => {
    if (base64Info.base64) {
      chooseImgFetch(base64Info.base64, base64Info.picType)
    }
  }, [base64Info.timestamp])

  // 选择图片
  const chooseImage = () => {
    setUpModal()
    if (isApp() && isH5) {
      citongImg()
    } else {
      fileInput.current?.click()
    }
  }

  // H5选择图片
  const fileChange = (e) => {
    const file = e.target.files[0]
    if (!file) {
      return
    }
    fileInput.current!.value = ''
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = (res) => {
      setBase64Info({
        timestamp: dayjs().unix(),
        picType: 1,
        base64: res.target?.result as string
      })
    }
  }

  // app选择图片
  const citongImg = () => {
    SchemaToApp('miaobiai://art?type=fun&method=selectImage', (res) => {
      if (!res.data) {
        Taro.showToast({
          title: `请重新选择图片`,
          icon: 'none'
        })
        return
      }
      if (res.data.indexOf(';base64,') == -1) {
        // 兼容处理，安卓获取的图片base64码没有前缀，而苹果也没有,base64前缀并不固定
        res.data = 'data:image/jpeg;base64,' + res.data // 加上base64前缀
      }
      console.log('app选择图片', res)
      setBase64Info({
        timestamp: dayjs().unix(),
        picType: res.imageType,
        base64: res.data
      })
    })
  }

  // 删除
  const [removeState, removeFetch] = useAsyncFn(async () => {
    let index = -1
    let id = 0
    prepareListState.value.forEach((item, i) => {
      if (item.image === activeUrl) {
        index = i
        id = item.id
      }
    })
    if (index === -1) {
      return ''
    }
    const res = await aiDanceRemovePic({
      id,
      uid: user.id
    })
    if (res.code !== 200) {
      return ''
    }
    prepareListFetch().then(() => {
      setTimeout(() => {
        if (index < 1) {
          mainSwiper?.slideTo(0, 0)
        } else {
          mainSwiper?.slideTo(index, 0)
        }
      }, 200)
    })
    setDelModal()
    return res.payload
  }, [activeUrl, prepareListState])

  // 生成
  const [generateState, generateFetch] = useAsyncFn(async () => {
    const res = await aiDanceSubmit({
      uid: user.id,
      toolId: router.params.toolId,
      toolName: styleData?.toolName,
      styleId: styleData?.styleId,
      styleName: styleData?.styleName,
      modelType: router.params.modelType,
      inputPic: activeUrl
    })
    if (res.code !== 200) {
      return []
    }
    SchemaToApp(
      `miaobiai://art?type=fun&method=reloadData&json=${JSON.stringify({
        source: 'AICreateList'
      })}`,
      () => {}
    )
    Taro.navigateTo({
      url: `/pages/ai-job-detail/index?jobId=${res.payload}&toolsType=${router.params.toolsType}&toolId=${router.params.toolId}&from=ai-job`
    })
    return
  }, [activeUrl])

  return (
    <div className="w-full h-full" style={{ backgroundColor: conf.bgColor, fontFamily: 'HYZhengYuan-55W' }}>
      <div className="w-full h-full flex flex-col">
        <div className="flex-1 w-full overflow-auto pt-[38px] md:pt-[1.69vw] md:flex md:flex-col md:justify-evenly">
          <div className="w-full md:h-full md:flex md:flex-col md:justify-center">
            <div
              className="font-semibold text-[24px] mb-[24px] md:text-[2.52vw] md:mb-[2.93vw] text-black leading-none text-center"
              onClick={() => {
                mainSwiper?.slideTo(1, 0)
              }}
            >
              {styleData?.styleName}
            </div>
            <div className="w-[750px] md:w-[100vw]">
              <Swiper
                className="rootSwiper h-[881px] md:h-[64.31vw]"
                onActiveIndexChange={(e) => {
                  setActiveUrl(prepareListState.value[e.activeIndex - 1]?.image || '')
                }}
                watchSlidesProgress
                resistanceRatio={0}
                thumbs={{ swiper: thumbsSwiper }}
                onSwiper={(e) => setMainSwiper(e)}
                onSetTranslate={function (swiper) {
                  const slides = swiper.slides
                  const offsetAfter = swiper.width * 0.9 //每个slide的位移值
                  for (let i = 0; i < slides.length; i++) {
                    const slide = slides.eq(i)
                    const slidedom: any = slides[i]
                    const progress = slidedom.progress
                    slide.transform('translate3d(' + progress * offsetAfter + 'px, 0, 0) scale(' + (1 - Math.abs(progress) / 10) + ')')
                    slide.css('zIndex', (1 - Math.abs(progress) / 20) * 100)
                  }
                }}
                onSetTransition={function (swiper, transition) {
                  for (var i = 0; i < swiper.slides.length; i++) {
                    const slide = swiper.slides.eq(i)
                    slide.transition(transition)
                  }
                }}
              >
                <SwiperSlide className="h-[871px] md:h-[64.31vw]">
                  <div
                    onClick={setUpModal}
                    className="text-center bg-white m-auto rounded-[33px] md:rounded-[3.26vw] overflow-hidden w-[65.34%] h-[871px] md:w-[36.18%] md:h-[64.31vw] swiper-slide-higt flex flex-col items-center anim_btn"
                  >
                    <ProgressLoading time={2000} state={chooseImgState.loading} />
                    {chooseImgState.loading ? null : (
                      <>
                        <img className="w-[66px] h-[66px] mt-[275px] mb-[390px] md:w-[5.44vw] md:h-[5.44vw] md:mt-[18.49vw] md:mb-[28.48vw]" src={add} />
                        <div className="font-medium text-[24px] leading-[40px] md:text-[1.97vw] md:leading-[2.95vw] text-[#B4BFD9]">请上传作品/全身照</div>
                        <div className="font-medium text-[24px] leading-[40px] md:text-[1.97vw] md:leading-[2.95vw] text-[#B4BFD9]">上传作品或全身照时四肢无遮挡</div>
                      </>
                    )}
                    <input
                      onChange={fileChange}
                      onClick={(e) => {
                        e.stopPropagation()
                      }}
                      type="file"
                      ref={fileInput}
                      accept="image/*"
                      hidden
                    />
                  </div>
                </SwiperSlide>
                {prepareListState.value?.map((item) => (
                  <SwiperSlide key={item.image} className="h-[881px] md:h-[64.31vw] rounded-[33px] md:rounded-[3.26vw] overflow-hidden">
                    <div
                      style={{ boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px' }}
                      className={`text-center ai_bg_linear m-auto rounded-[33px] md:rounded-[3.26vw] overflow-hidden w-[65.34%] h-[871px] md:w-[36.18%] md:h-[64.31vw] relative ${
                        item.picType === 1 ? 'swiper-slide-higt' : 'swiper-slide-low'
                      }`}
                    >
                      <img src={item.image} className="w-full h-full object-cover" />
                      {activeUrl !== item.image ? <div className="swiper-slide-shadow swiper-slide-shadow-cards"></div> : null}
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>
          <div className="max-w-[750px] md:max-w-[100vw] mx-auto mt-[33px] md:mt-[3.97vw]">
            <Swiper className="navSwiper h-[344px] md:h-[28.08vw]" onSwiper={(e) => setThumbsSwiper(e)} slidesPerView="auto" freeMode watchSlidesProgress>
              <SwiperSlide className="w-[188px] h-[344px] md:w-[15.42vw] md:h-[28.08vw]">
                <div
                  className={`anim_btn text-center bg-white rounded-[10px] h-[334px] border-[7px] md:rounded-[0.98vw] md:h-[27.43vw] md:border-[0.65vw] overflow-hidden w-full flex_center box-border border-solid ${
                    activeUrl === '' ? 'border-[#FF8C90]' : 'border-white'
                  }`}
                >
                  <img className="w-[54px] h-[54px] md:w-[4.45vw] md:h-[4.45vw]" src={add} />
                </div>
              </SwiperSlide>

              {prepareListState.value?.map((item, i) => (
                <SwiperSlide key={item.image} className="w-[188px] h-[344px] md:w-[15.42vw] md:h-[28.08vw]">
                  <div
                    style={{ boxShadow: 'rgba(0, 0, 0, 0.16) 0px 1px 4px' }}
                    className={`relative anim_btn ai_bg_linear overflow-hidden text-center w-full rounded-[10px] h-[334px] md:rounded-[0.98vw] md:h-[27.43vw] box-border border-solid  ${
                      activeUrl === item.image ? 'border-[#FF8C90] border-[7px] md:border-[0.65vw]' : 'border-white border-[0]'
                    }`}
                  >
                    {item.id !== '0' && activeUrl === item.image ? (
                      <div
                        onClick={(e) => {
                          e.stopPropagation()
                          setDelModal()
                        }}
                        className="anim_btn absolute w-[35px] h-[35px] top-[5px] right-[5px] md:w-[2.86vw] md:h-[2.86vw] md:top-[0.46vw] md:right-[0.46vw] flex_center"
                      >
                        <img src={removeImg} alt="" className="w-full h-full object-cover" />
                      </div>
                    ) : null}
                    <img src={item.image} alt="" className="w-full h-full object-cover" />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>

        {/* 生成按钮 */}
        <div className="w-full m-auto h-[164px] md:h-[12.7vw] bg-[#FFEEE6] z-50 bottom-0 flex_center">
          <img onClick={activeUrl ? generateFetch : () => {}} className="mx-auto w-[674px] h-[101px] md:w-[93.62vw] md:h-[8.53vw] block md:hidden" src={activeUrl ? generated : generatedGreyImg}></img>
          <img
            onClick={activeUrl ? generateFetch : () => {}}
            className="mx-auto w-[674px] h-[101px] md:w-[93.62vw] md:h-[8.53vw] hidden md:block"
            src={activeUrl ? generatedMax : generatedGreyImgImg}
          ></img>
        </div>
      </div>

      {/* 生成Loading */}
      <MaskLoading state={generateState.loading} time={2000} />

      {/* 上传提示框 */}
      <Modal show={upModal} mode={1}>
        <div className="w-[750px] md:w-[100vw] h-screen relative">
          <div className="h-full w-full" onClick={setUpModal}></div>
          <div
            className="absolute left-0 bottom-[-20px] w-[750px] h-[1080px] mt-[20px] rounded-[33px_33px_0_0]
            md:bottom-[-1.6119vw] md:w-[100vw] md:h-[87.04vw] md:mt-[1.6119vw] md:rounded-[3.26vw_3.26vw_0_0]
          bg-white flex flex-col items-center"
          >
            <div className="w-[73px] h-[73px] top-[27px] right-[22px] md:w-[5.99vw] md:h-[5.99vw] md:top-[2.15vw] md:right-[3.26vw] anim_btn absolute" onClick={setUpModal}>
              <img src={closeImg} alt="" className="w-full h-full object-cover" />
            </div>

            <div className="font-medium text-[31px] md:text-[2.52vw] text-black leading-none mt-[37px] md:mt-[2.99vw]">上传形象建议</div>
            <div className="w-[674px] h-[316px] mt-[157px] md:w-[93.62vw] md:h-[26.04vw] md:mt-[10.83vw] relative">
              <div
                className="shadow-md w-[269px] h-[359px] rounded-[15px] top-[-97px] left-[54px]
              md:w-[22.01vw] md:h-[29.3vw] md:rounded-[15px] md:top-[-8.01vw] md:left-[4.56vw]
              absolute z-10 overflow-hidden"
              >
                <Swiper className="modelSwiper" loop autoplay={{ delay: 2000 }}>
                  <SwiperSlide className="w-full h-full">
                    <div className="w-full h-full">
                      <img className="w-full h-full" src={model1Img} />
                    </div>
                  </SwiperSlide>
                </Swiper>
              </div>
              <img className="w-full h-full block md:hidden" src={suggestion1Img} />
              <img className="w-full h-full hidden md:block" src={suggestion1MaxImg} />
            </div>
            <div className="w-[674px] h-[351px] mt-[21px] md:w-[93.75vw] md:h-[28.78vw] md:mt-[1.63vw]">
              <img className="w-full h-full block md:hidden" src={suggestion2Img} />
              <img className="w-full h-full hidden md:block" src={suggestion2MaxImg} />
            </div>

            <img onClick={chooseImage} src={uploadImg} className="mt-[20px] md:mt-[2.15vw] mx-auto w-[674px] h-[101px] md:w-[93.62vw] md:h-[8.53vw] flex_center anim_btn block md:hidden"></img>
            <img onClick={chooseImage} src={uploadMaxImg} className="mt-[20px] md:mt-[2.15vw] mx-auto w-[674px] h-[101px] md:w-[93.62vw] md:h-[8.53vw] flex_center anim_btn hidden md:block"></img>
          </div>
        </div>
      </Modal>

      {/* 删除二次确认框 */}
      <Modal show={delModal}>
        <div className="w-[626px] h-[738px] md:w-[51.37vw] md:h-[44.83vw]">
          <div className="w-full h-[439px] md:h-[36vw] relative flex_center">
            <img src={modal2Img} className="absolute z-0 w-full h-full"></img>
            <div className="relative font-normal text-[31px] mt-[20px] md:text-[2.52vw] md:mt-[1.6258vw] text-[#D95200] leading-[1] text-center">是否删除该图片</div>
          </div>

          <div className="w-[532px] h-[101px] mt-[11px] md:w-[43.45vw] md:h-[8.33vw] md:mt-[0.5vw] mx-auto flex justify-between items-center">
            <div onClick={setDelModal} className="anim_btn w-[254px] h-[101px] md:w-[20.8vw] md:h-[8.33vw]">
              <img src={cancelImg} className="w-full h-full"></img>
            </div>
            <Spin state={removeState.loading} onClick={removeFetch} className="anim_btn w-[254px] h-[101px] md:w-[20.8vw] md:h-[8.33vw]">
              <img src={confirmImg} className="w-full h-full"></img>
            </Spin>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default WithDataFetching(Index)
