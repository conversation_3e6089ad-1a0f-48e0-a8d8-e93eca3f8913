import microphoneImg from '@/assets/images/ai-box/microphone.png'
import microphoneActiveImg from '@/assets/images/ai-box/microphone-active.png'
import waveGif from '@/assets/images/ai-box/wave.gif'
import generated from '@/assets/images/ai-story/generated.png'
import generatedMax from '@/assets/images/ai-story/generated-max.png'
import bj1Img from '@/assets/images/ai-box/bj1.png'
import bj2Img from '@/assets/images/ai-box/bj2.png'
import bj3Img from '@/assets/images/ai-box/bj3.png'
import modal3Img from '@/assets/images/ai-box/modal3.png'
import step1Img from '@/assets/images/ai-story/step.png'
import stepd1Img from '@/assets/images/ai-story/step-active.png'
import logo1Img from '@/assets/images/ai-story/logo1.png'
import logo2Img from '@/assets/images/ai-story/logo2.png'
import optionBoxImg from '@/assets/images/ai-story/option-box.png'
import maskImg from '@/assets/images/ai-story/mask.png'
import photoImg from '@/assets/images/ai-story/photo.png'
import hornImg from '@/assets/images/ai-story/horn.png'
import horn1Img from '@/assets/images/ai-story/horn1.png'
import speakImg from '@/assets/images/ai-story/speak.png'
import startImg from '@/assets/images/ai-story/start.png'
import waitImg from '@/assets/images/ai-story/wait.png'
import jiaoImg from '@/assets/images/ai-story/jiao.png'
import msgBox2Img from '@/assets/images/ai-story/msg-box2.png'
import Taro, { useDidHide, useRouter } from '@tarojs/taro'
import { SchemaToApp, isApp, isH5, resizeBase64Img } from '@/utils'
import { useEffect, useRef, useState } from 'react'
import { defAnimate, maskAnimate } from '@/components/Modal'
import { useAsyncFn, useInterval } from 'react-use'
import MaskLoading from '@/components/MaskLoading'
import { AnimatePresence, motion } from 'framer-motion'
import SwiperCore from '@msb-next/swiper'
import { Swiper, SwiperSlide } from '@msb-next/swiper/react'
import '@msb-next/swiper/swiper-bundle.min.css'
import ProgressLoading from '@/components/ProgressLoading'
import { aiStoryBookImage, aiStoryCompact, aiStoryCover, aiStoryCoverByImage, aiStoryFirstPage, aiStoryoTherPage } from '@/api/ai-story'
import useGetState from '@/hooks/useGetState'
import { useAtomValue } from 'jotai'
import { refreshTxt2imgState } from '@/store/ai-box'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import { boxConfState } from '@/store/global'
import WithDataFetching from '@/components/WithDataFetching'

const StepIcon = [step1Img, step1Img, step1Img, step1Img, step1Img, step1Img]
const StepIconActive = [stepd1Img, stepd1Img, stepd1Img, stepd1Img, stepd1Img, stepd1Img]

const GifList = {
  start: startImg,
  speak: speakImg,
  wait: waitImg
}

interface AudioItem {
  text?: string // 选项文案
  tips?: string // 对话框文案
  audioUrl: string
  // ABCD选项索引,为数字时表示音频正在播放这个选项
  optionIndex?: number
}

const OptionList = ['A', 'B', 'C', 'D']

function Index() {
  const router = useRouter()
  const [canRecording, setCanRecording] = useState(false) // 录音状态
  // const refreshTxt2img = useAtomValue(refreshTxt2imgState); // 重置用随机数
  const conf = useAtomValue(boxConfState)[router.params.toolId || '9']
  // step  页面   操作
  // -1:   主题页 调用aiStoryFirstPage 生成第一页
  //  0:   第一页 调用aiStoryoTherPage({ pageNum: 2 }) 生成第二页
  //  1:   第二页 调用aiStoryoTherPage({ pageNum: 3 }) 生成第三页
  //  2:   第三页 调用aiStoryoTherPage({ pageNum: 4 }) 生成第四页
  //  3:   第四页 调用aiStoryoTherPage({ pageNum: 5 }) 生成第五页
  //  4:   第五页 调用aiStoryoTherPage({ pageNum: 6 }) 生成第六页
  //  5:   第六页 调用aiStoryCompact 生成绘本
  // 每页生成成功后step+1,调用aiStoryBookImage获取页面图片
  const [step, setStep] = useState(-1) // 步骤
  const [mainSwiper, setMainSwiper] = useState<SwiperCore | null>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const [audioIndex, setAudioIndex, getAudioIndex] = useGetState(0)
  const [interval, setInterval] = useState<number | null>(null) // 轮询间隔
  const [bookSdId, setBookSdId] = useState(0) // 绘本页详情id
  const [bookId, setBookId] = useState(0) // 绘本id
  const [gifIndex, setGifIndex, getGifIndex] = useGetState<'wait' | 'speak' | 'start'>('wait') // gif索引
  const [audioList, setAudioList, getAudioList] = useGetState<AudioItem[]>([]) // 音频列表
  const [pageStoryAudioUrl, setPageStoryAudioUrl] = useState('') // 页面故事
  const [storyThemeList, setStoryThemeList] = useState<any[]>([]) // 主题列表
  const [storyQuestion, setStoryQuestion] = useState<AudioItem | null>(null) // 问题
  const [defaultProgress, setDefaultProgress] = useState(0) // 默认进度
  const refreshTxt2img = useAtomValue(refreshTxt2imgState) // 重置用随机数
  const fileInput = useRef<HTMLInputElement>(null)
  const [mainRole, setMainRole] = useState('') // 主角

  // 获取json
  const [storyState, storyFetch] = useAsyncFn(async (base64?: string) => {
    addSpeak([])
    // 生成封面故事
    const res = base64
      ? await aiStoryCoverByImage({
          base64: base64.split('base64,')[1],
          type: conf.type
        })
      : await aiStoryCover(conf.type)

    console.log(res)
    if (res.code !== 200) {
      return null
    }
    Taro.setNavigationBarTitle({
      title: res.payload.toolName || 'AI'
    })
    setStep(-1)
    addSpeak([
      { tips: res.payload.tips, audioUrl: res.payload.audioUrl },
      // res.payload.question,
      ...res.payload.storyThemeList.map((v, i) => ({ ...v, optionIndex: i }))
    ])
    setStoryThemeList(res.payload.storyThemeList)
    setStoryQuestion({
      text: res.payload.tips,
      audioUrl: res.payload.audioUrl
    })
    setMainRole(res.payload.mainRole)
    // 欢迎
    setGifIndex('start')
    setTimeout(() => {
      setGifIndex('speak')
    }, 1520)
    return res.payload
  }, [])

  useDidHide(() => {
    addSpeak([])
  })

  // 追加音频
  const addSpeak = (list: AudioItem[]) => {
    setAudioIndex(0)
    setAudioList(list)
  }

  // 初始化
  useEffect(() => {
    console.log('refreshTxt2img', refreshTxt2img)
    // 关闭分享
    SchemaToApp('miaobiai://art?type=fun&method=showShareButton&isShow=0', () => {})
    // 获取封面故事
    storyFetch()
  }, [refreshTxt2img])

  // 音频播放完毕自动下标指向下一个
  useEffect(() => {
    audioRef.current!.onplay = () => {
      // console.log("onplaying", audioRef.current?.played);
    }
    audioRef.current!.onpause = () => {
      // console.log("onpause", audioRef.current?.paused);
      setGifIndex('wait')
    }
    audioRef.current!.onended = () => {
      const index = getAudioIndex()
      const list = getAudioList()
      // 最后一个
      if (index === list.length - 1) {
        setGifIndex('wait')
        addSpeak([])
      } else {
        setAudioIndex((v) => v + 1)
      }
    }
    audioRef.current!.oncanplay = () => {
      console.log('oncanplay', audioRef.current)
      audioRef.current?.play().catch(() => {})
    }
  }, [])

  // 根据下标逐步播放音频
  useEffect(() => {
    if (audioRef.current) {
      if (audioList.length) {
        console.log('播放', audioIndex, audioList[audioIndex])
        if (audioList.length && audioList[audioIndex] && audioList[audioIndex].audioUrl) {
          audioRef.current.src = audioList[audioIndex].audioUrl
          audioRef.current.play().catch(() => {})
          // console.log('src', audioList[audioIndex].audioUrl)
          getGifIndex() !== 'start' && setGifIndex('speak')
        } else {
          audioRef.current.pause()
        }
      } else {
        audioRef.current.pause()
      }
    }
  }, [audioList, audioIndex])

  // 录音
  const microphoneClick = () => {
    //检查录音权限
    SchemaToApp('miaobiai://art?type=fun&method=checkAudioPermission', (premise) => {
      console.log('app录音 开始', premise)
      setCanRecording(true)
      //开始录音
      SchemaToApp('miaobiai://art?type=fun&method=recordingForSdk', (res) => {
        console.log('app录音 结束', res)
        if (res.data) {
          pageGenerateFetch(res.data)
        } else {
          Taro.showToast({
            title: `没有识别到语音内容,请重新录制`,
            icon: 'none'
          })
        }
        setCanRecording(false)
      })
    })
  }

  // 选择图片
  const chooseImage = () => {
    addSpeak([])
    if (isApp() && isH5) {
      citongImg()
    } else {
      fileInput.current?.click()
    }
  }

  // H5选择图片
  const fileChange = (e) => {
    const file = e.target.files[0]
    if (!file) {
      return
    }
    fileInput.current!.value = ''
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = (res) => {
      resizeBase64Img(res.target?.result as string).then((img) => {
        storyFetch(img.base64)
      })
    }
  }

  // APP选择图片
  const citongImg = () => {
    SchemaToApp('miaobiai://art?type=fun&method=selectAPhoto&isCrop=true', (res) => {
      if (!res.data) {
        Taro.showToast({
          title: `请重新选择图片`,
          icon: 'none'
        })
        return
      }
      if (res.data.indexOf(';base64,') == -1) {
        // 兼容处理，安卓获取的图片base64码没有前缀，而苹果也没有,base64前缀并不固定
        res.data = 'data:image/jpeg;base64,' + res.data // 加上base64前缀
      }
      console.log('app选择图片', res.data)
      resizeBase64Img(res.data).then((img) => {
        storyFetch(img.base64)
      })
    })
  }

  // 生成页面
  const [pageGenerateState, pageGenerateFetch] = useAsyncFn(
    async (text) => {
      addSpeak([])
      const res = step < 0 ? await aiStoryFirstPage({ text, type: conf.type, mainRole }) : await aiStoryoTherPage({ text, pageNum: step + 2, bookId })
      console.log(res)
      if (res.code !== 200) {
        return {}
      }
      addSpeak([
        res.payload.story,
        res.payload.question,
        ...(res.payload.chooseList || []).map((v, i) => ({
          ...v,
          optionIndex: i
        }))
      ])
      if (step < 0) {
        setBookId(res.payload.bookId)
      }
      setPageStoryAudioUrl(res.payload.story?.audioUrl || '')
      setBookSdId(res.payload.bookSdId)
      setStoryThemeList(res.payload.chooseList)
      mainSwiper?.slideNext()
      setStep((v) => v + 1)
      setInterval(5000)
      setStoryQuestion(res.payload.question)
      return res.payload
    },
    [bookId, mainRole, mainSwiper, step]
  )

  // 获取页面图片
  const [bookImageState, bookImageFetch] = useAsyncFn(async () => {
    const res = await aiStoryBookImage({ bookSdId, bookId })
    console.log(res)
    if (res.code !== 200) {
      setInterval(null)
      return {}
    }
    if (res.payload.progress >= 100) {
      setInterval(null)
      setDefaultProgress(0)
    } else {
      setDefaultProgress(res.payload.progress)
    }
    return res.payload
  }, [bookSdId, bookId])

  // 轮询调用获取页面图片
  useInterval(() => {
    bookImageFetch()
  }, interval || null)

  // 生成绘本
  const [generateState, generateFetch] = useAsyncFn(async () => {
    setInterval(null)
    addSpeak([])
    const res = await aiStoryCompact({ bookId, type: conf.type })
    console.log(res)
    if (res.code !== 200) {
      return []
    }
    SchemaToApp(
      `miaobiai://art?type=fun&method=reloadData&json=${JSON.stringify({
        source: 'AICreateList'
      })}`,
      () => {}
    )
    Taro.navigateTo({
      url: `/pages/ai-job-detail/index?jobId=${res.payload.id}&toolsType=${storyState.value.toolsType}&toolId=${router.params.toolId}&from=ai-job`
    })
    return
  }, [storyState, bookId])

  return (
    <div className="min-h-full w-full h-auto md:h-full mx-auto flex flex-col" style={{ backgroundColor: conf.bgColor, fontFamily: 'HYZhengYuan-55W' }}>
      {/* 背景 */}
      <img className="absolute w-[143px] h-[281px] md:w-[11.726vw] md:h-[23.042vw] right-0" src={bj1Img} />
      <img className="absolute w-[257px] h-[212px] top-[612px] md:w-[21.074vw] md:h-[17.384vw] md:top-[50.184vw]" src={bj2Img} />
      <img className="absolute w-[305px] h-[197px] right-0 bottom-[156px] md:w-[25.01vw] md:h-[16.154vw] md:right-0 md:bottom-[12.7vw]" src={bj3Img} />

      <audio preload="auto" ref={audioRef} />

      {/* 进度 */}
      <div className="mx-auto w-[674px] h-[59px] mt-[20px] md:w-[55.268vw] md:h-[5.21vw] md:mt-[1.64vw] flex_center relative">
        <div className={`flex_center h-full ${step < 0 ? 'w-[61px] md:w-[5.73vw]' : 'w-[49px] md:w-[4.82vw]'} mr-[41px] md:mr-[3.362vw]`}>
          <img className="w-full h-auto" src={step < 0 ? logo1Img : logo2Img} />
        </div>
        <div className="flex-1 h-full relative flex items-center justify-between">
          {StepIcon.map((item, index) => (
            <div key={index} className="w-[27px] h-[27px] md:w-[2.6vw] md:h-[2.6vw] bg-white rounded-full relative flex_center">
              <img className={`${step === index ? 'w-[58px] h-[59px] md:w-[5.66vw] md:h-[5.66vw]' : 'w-full h-full'}`} src={step === index ? StepIconActive[index] : item} />
              {index !== 5 ? <div className="w-[6px] h-[6px] right-[-44px] top-[12px] md:w-[0.72vw] md:h-[0.72vw] md:right-[-3.4vw] md:top-[1vw] bg-white rounded-full absolute"></div> : null}
            </div>
          ))}
        </div>
      </div>

      <Swiper onSwiper={(e) => setMainSwiper(e)} className="w-full flex-1 min-h-[1200px] md:min-h-[83vw]" allowTouchMove={false} loop>
        <SwiperSlide className="flex flex-col justify-evenly">
          {/* 步骤-1~4 */}
          {step < 5 ? (
            <>
              {/* 内容 */}
              <div className="mx-auto w-[674px] h-[695px] md:w-[55.268vw] md:h-[53.77vw] relative">
                {/* 小熊gif */}
                <img className="absolute z-10 w-[304px] bottom-[10px] left-[-40px] md:w-[23.67vw] md:bottom-[0vw] md:left-[-1.28vw]" src={GifList[gifIndex]} />
                {/* 对话框A */}
                {step === -1 ? (
                  <div className="bg-white rounded-[40px] pb-[92px] w-[415px] min-h-[300px] right-[30px] top-[57px] md:rounded-[3.91vw] md:pb-[6.3vw] md:w-[34.04vw] md:min-h-[26.18vw] md:top-[2.47vw] absolute">
                    <img className="w-[57px] h-[31px] absolute bottom-[-29px] left-[72px] md:w-[4.62vw] md:h-[2.47vw] md:bottom-[-2.3vw] md:left-[5.86vw] z-0" src={jiaoImg} />
                    <input onChange={fileChange} type="file" ref={fileInput} accept="image/*" hidden />
                    {conf.hasCamera ? (
                      <img
                        onClick={chooseImage}
                        className="anim_btn absolute w-[65px] h-[56px] right-[146px] bottom-[16px] md:w-[5.4vw] md:h-[4.69vw] md:right-[11.98vw] md:bottom-[1.5vw] z-10"
                        src={photoImg}
                      />
                    ) : null}
                    <img
                      onClick={() => {
                        if (storyQuestion) addSpeak([storyQuestion])
                      }}
                      className="anim_btn absolute w-[66px] h-[59px] right-[28px] bottom-[16px] md:w-[5.54vw] md:h-[4.94vw] md:right-[2.28vw] md:bottom-[1.3vw] z-10"
                      src={horn1Img}
                    />
                    <div className="relative text-[31px] leading-[44px] md:text-[2.52vw] md:leading-[3.28vw] text-[#D56604] mt-[35px] mx-[33px] md:mt-[3.4vw] md:mx-[3.2vw] text-justify">
                      {audioList[audioIndex]?.tips || storyQuestion?.text}
                    </div>
                  </div>
                ) : (
                  <>
                    {/* 对话框B */}
                    <div className="w-[278px] h-[261px] left-0 top-[30px] md:w-[22.78vw] md:h-[21.42vw] md:left-[-1.28vw] md:top-[3.4vw] absolute">
                      <img className="w-full h-full absolute z-0" src={msgBox2Img} />
                      <img
                        onClick={() => {
                          if (storyQuestion) addSpeak([storyQuestion])
                        }}
                        className="anim_btn absolute w-[66px] h-[59px] right-[25px] bottom-[31px] md:w-[5.73vw] md:h-[5.08vw] md:right-[1.82vw] md:bottom-[2.56vw] z-10"
                        src={horn1Img}
                      />
                      <div className="relative text-[#D56604] text-[31px] leading-[40px] mt-[28px] mx-[28px] md:text-[2.51vw] md:leading-[3.28vw] md:mt-[2.1vw] md:mx-[2.1vw] text-justify break_anywhere">
                        {audioList[audioIndex]?.tips || storyQuestion?.text}
                      </div>
                    </div>
                    <div
                      className="w-[356px] h-[634px] right-0 top-[30px] rounded-[40px] md:w-[29.23vw] md:h-[51.97vw] md:right-[-1vw] md:top-[0.4vw] md:rounded-[3.91vw] overflow-hidden absolute"
                      style={{ backgroundColor: conf.darkColor }}
                    >
                      {/* 底图 */}
                      {!interval ? <ImgPlaceholder className="w-full h-full absolute z-0" src={bookImageState.value?.imageUrl} /> : null}
                      {/* 喇叭 */}
                      <img
                        onClick={() => addSpeak([{ audioUrl: pageStoryAudioUrl }])}
                        className="w-[67px] h-[60px] top-[25px] right-[15px] md:w-[5.54vw] md:h-[4.94vw] md:top-[2vw] md:right-[2vw] anim_btn absolute z-20"
                        src={horn1Img}
                      />
                      {/* 进度条 */}
                      <ProgressLoading time={2000} defaultProgress={defaultProgress} state={interval ? true : false} quiet txt={<span className="text-white">正在生成中请稍等</span>} />
                    </div>
                  </>
                )}
              </div>
              {/* 剧情分割 */}
              <div className="mx-auto w-[674px] h-[41px] md:h-[5.71vw] flex_center relative">
                <img className="absolute h-full" src={conf.pictureBg} />
              </div>
              {/* 选项 */}
              <div className="mx-auto md:w-[54.54vw] w-[666px] mt-[12px] relative flex-wrap justify-between flex">
                {storyThemeList.map((item, index) => (
                  <div key={index} className="w-[326px] h-[224px] mb-[17px] md:mb-[1vw] md:w-[26.77vw] md:h-[18.4vw] relative anim_btn" onClick={() => pageGenerateFetch(item.text)}>
                    <img className="absolute z-10 w-full h-full" src={optionBoxImg} />
                    <div className="absolute z-20 w-full h-full">
                      <div className="mt-[14px] ml-[71px] md:mt-[1.4vw] md:ml-[6vw] font-normal text-[33px] md:text-[2.35vw] leading-[33px] md:leading-[2.35vw]  text-left text-white not-italic normal-case">
                        {OptionList[index]}
                      </div>
                      <div className="mt-[17px] px-[20px] md:mt-[1.4vw] md:px-[2vw] font-normal text-[27px] md:text-[1.88vw] text-[#D56604] not-italic normal-case">{item.text}</div>
                      <img
                        onClick={(e) => {
                          e.stopPropagation()
                          e.preventDefault()
                          addSpeak([{ ...item, optionIndex: index }])
                        }}
                        className="absolute w-[64px] h-[65px] right-[30px] bottom-[15px] md:w-[4.5vw] md:h-[4.5vw] md:right-[1vw] md:bottom-[1vw] anim_btn"
                        src={hornImg}
                      />
                    </div>
                    {audioList[audioIndex]?.optionIndex !== index && audioList[audioIndex]?.optionIndex !== undefined ? <img className="absolute z-30 w-full h-full" src={maskImg} /> : null}
                  </div>
                ))}
              </div>
            </>
          ) : (
            // 步骤5
            <div className="mt-[35px] mx-auto w-[670px] h-[1191px] md:w-[50.96vw] md:h-[90.76vw] rounded-[40px] md:rounded-[1.95vw] md:mr-[10vw] relative" style={{ backgroundColor: conf.darkColor }}>
              {/* 底图 */}
              {!interval ? <ImgPlaceholder className="w-full h-full absolute z-0 rounded-[40px] md:rounded-[1.95vw]" src={bookImageState.value?.imageUrl} /> : null}
              {/* 喇叭 */}
              <img
                onClick={() => addSpeak([{ audioUrl: pageStoryAudioUrl }])}
                className="w-[63px] h-[63px] top-[27px] right-[29px] md:w-[5vw] md:h-[5vw] md:top-[2vw] md:right-[2vw] anim_btn absolute z-20"
                src={hornImg}
              />
              {/* 对话框 */}
              <div className="w-[278px] h-[261px] bottom-[280px] right-[100px] md:w-[22.92vw] md:h-[21.42vw] md:bottom-[40vw] md:right-[58vw] absolute z-20">
                <img className="w-full h-full absolute z-0" src={msgBox2Img} />
                <img
                  onClick={() => {
                    if (storyQuestion) addSpeak([storyQuestion])
                  }}
                  className="anim_btn absolute w-[66px] h-[59px] right-[25px] bottom-[31px] md:w-[5.73vw] md:h-[5.08vw] md:right-[1.82vw] md:bottom-[2.56vw] z-10"
                  src={horn1Img}
                />
                <div className="relative text-[31px] leading-[40px] text-[#D56604] mt-[25px] mx-[28px] md:text-[2.51vw] md:leading-[3.28vw] md:mt-[1.3vw] md:mx-[2.1vw] text-justify break_anywhere">
                  {audioList[audioIndex]?.tips || storyQuestion?.text}
                </div>
              </div>
              {/* 小熊gif */}
              <img className="w-[279px] absolute left-[56px] bottom-[85px] md:w-[30.67vw] md:bottom-[0vw] md:left-[-32vw] z-20" src={GifList[gifIndex]} />
              {/* 进度条 */}
              <ProgressLoading time={2000} defaultProgress={defaultProgress} state={interval ? true : false} quiet txt={<span className="text-white">正在生成中请稍等</span>} />
            </div>
          )}
        </SwiperSlide>
      </Swiper>

      <div className="w-full h-[151px] md:h-[12.15vw]"></div>

      {/* 生成 */}
      {step === 5 ? (
        <div
          className="w-full mx-auto h-[152px] md:h-[12.4vw] fixed z-50 bottom-0 flex_center"
          // style={{ backgroundColor: conf.bgColor }}
        >
          <div onClick={generateFetch} className="mx-auto w-[672px] h-[100px] rounded-[40px] font-medium text-[33px] md:w-[93.55vw] md:h-[8.4vw] text-white flex_center anim_btn">
            <img className="w-full h-full block md:hidden" src={generated}></img>
            <img className="w-full h-full hidden md:block" src={generatedMax}></img>
          </div>
        </div>
      ) : (
        <div className="mx-auto w-full h-[151px] fixed z-50 md:h-[12.15vw] flex_center bottom-0" style={{ backgroundColor: conf.bgColor }}>
          {/* 录音弹窗 */}
          <AnimatePresence>
            {canRecording ? (
              <>
                <motion.div {...maskAnimate} className="fixed bg-[#00000050] md:bg-transparent w-full h-full top-0 left-0 z-[701]"></motion.div>
                <motion.div {...defAnimate} className="flex_center absolute z-[702] w-[719px] h-[269px] bottom-[142px] md:w-[70.25vw] md:h-[26.3vw] md:bottom-[13vw]">
                  <img className="w-full h-full absolute z-0" src={modal3Img} />
                  <img className="w-[400px] h-[35px] mt-[65px] md:w-[40vw] md:h-[3.5vw] md:mt-[5.8vw] relative" src={waveGif} />
                </motion.div>
              </>
            ) : null}
          </AnimatePresence>
          {/* 录音按钮 */}
          <div
            onClick={(e) => {
              console.log('录音 onClick')
              e.stopPropagation()
              e.preventDefault()
              // 开始录音
              microphoneClick()
            }}
            className={`w-[111px] mx-auto h-[111px] md:w-[9.15vw] md:h-[9.15vw] rounded-full flex_center anim_btn ${canRecording ? 'z-[702]' : ''}`}
          >
            <img className="w-full h-full touch_callout pointer-events-none" src={canRecording ? microphoneActiveImg : microphoneImg} />
          </div>
        </div>
      )}

      {/* 生成Loading */}
      <MaskLoading state={storyState.loading || pageGenerateState.loading || generateState.loading} time={20000} />
    </div>
  )
}

export default WithDataFetching(Index)
