import Taro, { usePageScroll, useRouter } from '@tarojs/taro'
import { aiDanceModeList } from '@/api/ai-dance'
import { useAsyncFn } from 'react-use'
import { useEffect, useRef, useState } from 'react'
import { SchemaToApp } from '@/utils'
import tagImg from '@/assets/images/ai-dance/tag.png'
import { refreshTxt2imgState } from '@/store/ai-box'
import { useAtomValue } from 'jotai'
import bitmapImg from '@/assets/images/ai-dance/bitmap.png'
import { boxConfState } from '@/store/global'
import WithDataFetching from '@/components/WithDataFetching'

function Index() {
  const router = useRouter()
  const conf = useAtomValue(boxConfState)[router.params.toolId || '1']
  const videosRef = useRef<HTMLVideoElement[]>([])
  const refreshTxt2img = useAtomValue(refreshTxt2imgState)
  const [played, setPlayed] = useState<boolean[]>([])

  // 获取详情
  const [listState, listFetch] = useAsyncFn(async () => {
    const res = await aiDanceModeList()
    if (res.code !== 200) {
      return null
    }
    return res.payload
  }, [])

  // 初始化
  useEffect(() => {
    Taro.setNavigationBarTitle({
      title: 'AI舞蹈'
    })
    // 关闭分享
    SchemaToApp('miaobiai://art?type=fun&method=showShareButton&isShow=0', () => {})
    // 获取详情
    listFetch().then(() => {
      setTimeout(() => {
        const videos = Array.from(document.querySelectorAll('video'))
        videosRef.current = videos
        scroll()
      }, 300)
    })
  }, [])

  useEffect(() => {
    setTimeout(() => {
      const videos = Array.from(document.querySelectorAll('video'))
      videosRef.current = videos
      scroll()
    }, 100)
  }, [refreshTxt2img])

  usePageScroll(() => {
    scroll()
  })

  const isElementInViewport = (el) => {
    const rect = el.getBoundingClientRect()
    return (
      rect.top >= -(rect.height / 2) &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) + rect.height / 2 &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    )
  }

  const scroll = () => {
    const inView = videosRef.current.map((video) => isElementInViewport(video))
    videosRef.current.forEach((video, i) => {
      if (!inView[i]) {
        video.pause()
      }
    })
    videosRef.current.forEach((video, i) => {
      if (isElementInViewport(video)) {
        try {
          video.play().catch(() => {})
        } catch (error) {}
      }
    })
  }

  const jump = (modelType) => {
    Taro.navigateTo({
      url: `/pages/ai-dance/index?modelType=${modelType}&toolsType=${router.params.toolsType}&toolId=${router.params.toolId}`
    })
  }

  return (
    <div className="w-full min-h-full m-auto select-none flex flex-col" style={{ backgroundColor: conf.bgColor, fontFamily: 'HYZhengYuan-55W' }}>
      <div className="w-[680px] m-auto font-normal text-[33px] my-[37px] md:w-[93.36vw] md:text-[2.52vw] md:my-[2.6vw] text-black leading-none">舞蹈模板库</div>
      <div className="w-[680px] md:w-[93.36vw] mx-auto flex flex-wrap justify-between">
        {listState.value?.map((item, i) => (
          <div key={item.id} className="flex flex-col relative anim_btn" onClick={() => jump(item.modelType)}>
            {item.picType === 2 ? <img className="absolute w-[105px] h-[39px] md:w-[9.5752vw] md:h-[3.5565vw] left-0 top-0" src={tagImg} /> : null}
            {item.previewShow ? (
              <>
                <video
                  webkit-playsinline="true"
                  playsInline
                  loop
                  // poster={bitmapImg}
                  onCanPlay={() => {
                    console.log('onCanPlay' + i)
                    setPlayed((v) => {
                      v[i] = true
                      return [...v]
                    })
                  }}
                  src={item.previewShow}
                  className="w-[327px] h-[488px] rounded-[20px] md:w-[29.82vw] md:h-[53.02vw] md:rounded-[1.95vw] shadow-md object-cover"
                />
                {!played[i] ? (
                  <div className="absolute top-0 left-0 overflow-hidden w-[327px] h-[488px] rounded-[20px] md:w-[29.82vw] md:h-[53.02vw] md:rounded-[1.95vw] shadow-md bg-gray-200 flex_center">
                    <img className="w-full h-full object-cover" src={bitmapImg} alt="" />
                  </div>
                ) : null}
              </>
            ) : (
              <div className="w-[327px] h-[488px] rounded-[20px] md:w-[29.82vw] md:h-[53.02vw] md:rounded-[1.95vw] shadow-md bg-gray-200 flex_center">
                <img className="w-full" src={bitmapImg} alt="" />
              </div>
            )}
            <div className="text-[28px] md:text-[1.97vw] text-black leading-none my-[24px] md:my-[2.15vw] text-center">{item.modelName}</div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default WithDataFetching(Index)
