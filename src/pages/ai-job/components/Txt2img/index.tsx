import add from '@/assets/images/ai-box/add.png'
import trashcanImg from '@/assets/images/ai-box/trashcan.png'
import microphoneImg from '@/assets/images/ai-box/microphone.png'
import microphoneActiveImg from '@/assets/images/ai-box/microphone-active.png'
import closeImg from '@/assets/images/ai-box/close.png'
import waveGif from '@/assets/images/ai-box/wave.gif'
import generatedGreyImg from '@/assets/images/ai-box/generated-grey.png'
import generated from '@/assets/images/ai-box/generated.png'
import generatedGreyMaxImg from '@/assets/images/ai-box/generated-grey-max.png'
import generatedMax from '@/assets/images/ai-box/generated-max.png'
import styleCheckImg from '@/assets/images/ai-box/style-check.png'
import bj1Img from '@/assets/images/ai-box/bj1.png'
import bj2Img from '@/assets/images/ai-box/bj2.png'
import bj3Img from '@/assets/images/ai-box/bj3.png'
import modal3Img from '@/assets/images/ai-box/modal3.png'
import styleImg from '@/assets/images/ai-box/style.png'
import switchOnImg from '@/assets/images/ai-box/switch-on.png'
import switchOffImg from '@/assets/images/ai-box/switch-off.png'
import Taro, { useRouter } from '@tarojs/taro'
import { SchemaToApp, convertImgToBase64, resizeBase64Img, getUser, getVersion, isApp, isH5, versionInfo } from '@/utils'
import { MouseEvent, useEffect, useRef, useState } from 'react'
import { defAnimate, maskAnimate } from '@/components/Modal'
import { aiAvatarAudio2Text, aiAvatarCreate, aiAvatarDetail, aiAvatarPic2Text, aiAvatarToolDetail } from '@/api/ai-avatar'
import { useAsyncFn } from 'react-use'
import Spin from '@/components/Spin'
import { Detail } from '@/pages/ai-job-detail/components/Txt2img'
import Recorder from 'recorder-core/recorder.mp3.min'
import dayjs from 'dayjs'
import ProgressLoading from '@/components/ProgressLoading'
import MaskLoading from '@/components/MaskLoading'
import { AnimatePresence, motion } from 'framer-motion'
import { useAtomValue } from 'jotai'
import { refreshTxt2imgState } from '@/store/ai-box'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import { boxConfState } from '@/store/global'
import WithDataFetching from '@/components/WithDataFetching'

export enum imageType {
  'txt2img' = 'txt2img',
  'img2img' = 'img2img',
  'extras' = 'extras'
}

function Index() {
  const user = getUser()
  const router = useRouter()
  const [base64Info, setBase64Info] = useState<{
    width: number
    height: number
    timestamp?: number
    source: string
    base64: string
    pass: boolean
    generate: boolean
  }>({
    width: 512,
    height: 512,
    // 解决重复图片不触发effect问题
    timestamp: 0,
    source: '',
    base64: '',
    // 是否需要审核
    pass: false,
    // 是否需要图转文
    generate: false
  })
  const recorder = useRef<any>(null)
  const [canRecording, setCanRecording] = useState(false) // 录音状态
  const fileInput = useRef<HTMLInputElement>(null)
  const [style, setStyle] = useState('') // 风格
  const [styleConf, setStyleConf] = useState({
    hr_scale: 1, // 高清修复倍数
    is_show_hr: true, // 是否显示高清修复
    enable_hr: false, // 高清修复默认值
    max_size: 512, // 最大尺寸
    hr_upscaler: 'ESRGAN_4x' // 高清修复模型
  }) // 风格
  const [description, setDescription] = useState<string[]>([]) // 描述词
  const [dynamicConfigs, setDynamicConfigs] = useState(null) // 动态配置
  const [formData, setFormData] = useState<any>(null) // 表单数据
  const [inputText, setInputText] = useState('') // 输入框文字
  const [enable_hr, setEnable_hr] = useState(false) // 高清修复
  const [voice, setVoice] = useState({
    base64: '',
    duration: 0,
    audioType: 'wav'
  }) // 录音
  const descriptionInput = useRef<HTMLInputElement>(null) // 描述词输入框
  const refreshTxt2img = useAtomValue(refreshTxt2imgState) // 重置用随机数
  const conf = useAtomValue(boxConfState)[router.params.toolId || '1']
  const [changeIndex, setChangeIndex] = useState(-1) // 修改标签的下标

  // 获取详情
  // const [detailState, detailFetch] = useAsyncFn<
  //   () => Promise<Detail | null>
  // >(async () => {
  //   const res = await aiAvatarDetail({
  //     id: router.params.jobId,
  //     uid: user.id,
  //   });
  //   if (res.code !== 200) {
  //     return null;
  //   }
  //   setStyle(res.payload.styleName);
  //   setDescription(res.payload.descWord.split(",").filter((v) => v));
  //   const base64 = await convertImgToBase64(res.payload.picInput);
  //   // 这里主要是为了获取宽高,生成接口需要
  //   resizeBase64Img(base64, styleConf.max_size).then((img) => {
  //     setBase64Info({
  //       ...img,
  //       base64,
  //       source: base64,
  //       pass: true,
  //       generate: false,
  //     });
  //   });
  //   return res.payload;
  // }, [styleConf]);

  // 获取json
  const [jsonState, jsonFetch] = useAsyncFn(async (id) => {
    const res = await aiAvatarToolDetail({ id })
    console.log(res)
    if (res.code !== 200) {
      return null
    }
    const json = JSON.parse(res.payload.jsonConfig)
    Taro.setNavigationBarTitle({
      title: res.payload.toolName || 'AI'
    })
    console.log(json)
    setStyle(json.style[0].formData.name)
    setStyleConf(json.style[0].formData)
    setEnable_hr(json.style[0].formData.enable_hr)
    return json
  }, [])

  // 切换风格，动态配置/表单数据选中
  useEffect(() => {
    if (style) {
      jsonState.value?.style.forEach((item) => {
        if (item.formData.name === style) {
          setDynamicConfigs(item.dynamicConfigs)
          setFormData(item.formData)
        }
      })
    }
  }, [style])

  // 初始化
  useEffect(() => {
    setDescription([])
    setBase64Info({
      width: 512,
      height: 512,
      // 解决重复图片不触发effect问题
      timestamp: 0,
      source: '',
      base64: '',
      // 是否需要审核
      pass: false,
      // 是否需要图转文
      generate: false
    })
    // 关闭分享
    SchemaToApp('miaobiai://art?type=fun&method=showShareButton&isShow=0', () => {})
    // 获取详情
    jsonFetch(router.params.toolId).then(() => {
      if (router.params.jobId) {
        // 24.5.9产品移除回显
        // detailFetch();
      }
    })
  }, [refreshTxt2img])

  // 图片转文字
  const [chooseImgState, chooseImgFetch] = useAsyncFn(async (base64) => {
    const res = await aiAvatarPic2Text({ base64: base64.split('base64,')[1] })
    console.log(res)
    if (res.code !== 200) {
      setBase64Info((v) => ({ ...v, base64: '', pass: false }))
      return ''
    }
    setBase64Info((v) => ({ ...v, pass: true }))
    res.payload && setDescription(res.payload.split('，'))
    return res.payload
  }, [])

  // 图片转文字
  useEffect(() => {
    console.log(base64Info)
    if (base64Info.generate && base64Info.base64) {
      chooseImgFetch(base64Info.base64)
    }
  }, [base64Info.generate, base64Info.timestamp])

  // 录音转文字 & 文字转词组
  const [voiceState, voiceFetch] = useAsyncFn(async (data) => {
    const res = await aiAvatarAudio2Text({
      audioType: data.audioType,
      base64: data.base64.split('base64,')[1]
    })
    console.log(res)
    if (res.code !== 200) {
      return ''
    }
    if (!res.payload) {
      Taro.showToast({
        title: `没有识别到语音内容,请重新录制`,
        icon: 'none'
      })
    }
    setDescription((v) => {
      const all = [...v, ...res.payload.split(',')]
      // all 删除数组中的字符加起来超过200个的字符
      let count = 0
      let newStr: string[] = []
      all.forEach((item) => {
        count += item.length
        if (count <= 200) {
          newStr.push(item)
        } else {
          const remain = 200 - (count - item.length)
          newStr.push(item.slice(0, remain))
        }
      })
      return newStr
    })
    return res.payload
  }, [])

  // 录音转文字
  useEffect(() => {
    console.log(voice)
    if (voice.base64) {
      voiceFetch(voice)
    }
  }, [voice])

  // 录音
  const microphoneClick = async (state: boolean) => {
    if (description.join('').length >= 200) {
      Taro.showToast({
        title: `仅支持输入200字`,
        icon: 'none'
      })
      return
    }

    // 4.5.8原生录音
    // if (versionInfo.versionNumber >= 40508) {
    naviveMicrophone()
    return
    // }

    // // ios app录音
    // if (isApp() && getVersion().ios) {
    //   microphoneIos(state)
    //   return
    // }

    // // 安卓、H5 录音
    // if (state) {
    //   recorder.current = Recorder({
    //     type: 'mp3',
    //     sampleRate: 16000,
    //     bitRate: 16
    //   })
    //   recorder.current.open(
    //     () => {
    //       recorder.current.start()
    //       setCanRecording(true)
    //     },
    //     (msg, isUserNotAllow) => {
    //       console.log('getPermission', msg, isUserNotAllow)
    //       // H5 只能在https或localhost下使用麦克风
    //       console.log('H5 只能在https或localhost下使用麦克风', window.location.origin)
    //       setCanRecording(false)
    //       // if (isUserNotAllow) {
    //       if (isH5 && isApp()) {
    //         SchemaToApp(`miaobiai://art?type=fun&method=microphoneAuthority`, () => {})
    //       } else {
    //         Taro.showToast({
    //           title: `请先允许该网页使用麦克风`,
    //           icon: 'none'
    //         })
    //       }
    //       // }
    //     }
    //   )
    // } else {
    //   recorder.current.stop(
    //     (blob, duration) => {
    //       const reader = new FileReader()
    //       reader.onloadend = () => {
    //         const base64 = reader.result as string

    //         // test play
    //         // const localUrl = (window.URL || webkitURL).createObjectURL(blob);
    //         // const audio = document.createElement("audio");
    //         // document.body.prepend(audio);
    //         // audio.controls = true;
    //         // audio.src = localUrl;
    //         // audio.play();

    //         console.log('H5录音:', reader.result, '时长:' + duration + 'ms')
    //         setVoice({ base64, duration, audioType: 'mp3' })
    //         setCanRecording(false)
    //       }
    //       reader.readAsDataURL(blob)
    //       recorder.current.close()
    //       recorder.current = null
    //     },
    //     (msg, code) => {
    //       console.log(code)
    //       // Taro.showToast({
    //       //   title: "录音失败:" + msg,
    //       //   icon: "none",
    //       // });
    //       recorder.current.close() //可以通过stop方法的第3个参数来自动调用close
    //       recorder.current = null
    //       setCanRecording(false)
    //     }
    //   )
    // }
  }

  // ios录音
  const microphoneIos = (state: boolean) => {
    if (state) {
      // 开始录音
      SchemaToApp('miaobiai://art?type=fun&method=startRecord', () => {})
      setCanRecording(true)
    } else {
      // 结束录音
      SchemaToApp('miaobiai://art?type=fun&method=stopRecord', (res) => {
        console.log('app录音', res)
        setVoice({
          base64: `data:audio/wav;base64,${decodeURIComponent(res.data)}`,
          duration: res.msg,
          audioType: 'wav'
        })
        // test play
        // const audioInstance = new Audio();
        // audioInstance.src = `data:audio/mpeg;base64,${res.data}`;
        // audioInstance.play();
        setCanRecording(false)
      })
    }
  }

  // 4.5.8原生录音
  const naviveMicrophone = () => {
    //检查录音权限
    // miaobiai://art?type=fun&method=checkAudioPermission
    //开始录音
    // miaobiai://art?type=fun&method=recordingForSdk
    console.log('app录音 权限', 'checkAudioPermission')
    SchemaToApp('miaobiai://art?type=fun&method=checkAudioPermission', (premise) => {
      console.log('app录音 开始', premise)
      setCanRecording(true)
      SchemaToApp('miaobiai://art?type=fun&method=recordingForSdk', (res) => {
        console.log('app录音 结束', res)
        if (res.data) {
          setDescription((v) => {
            const all = [...v, res.data]
            // all 删除数组中的字符加起来超过200个的字符
            let count = 0
            let newStr: string[] = []
            all.forEach((item) => {
              count += item.length
              if (count <= 200) {
                newStr.push(item)
              } else {
                const remain = 200 - (count - item.length)
                newStr.push(item.slice(0, remain))
              }
            })
            return newStr.filter((s) => s)
          })
        } else {
          Taro.showToast({
            title: `没有识别到语音内容,请重新录制`,
            icon: 'none'
          })
        }

        setCanRecording(false)
      })
    })
  }

  // 选择图片
  const chooseImage = () => {
    if (isApp() && isH5) {
      citongImg()
    } else {
      fileInput.current?.click()
    }
  }

  // H5选择图片
  const fileChange = (e) => {
    const file = e.target.files[0]
    if (!file) {
      return
    }
    fileInput.current!.value = ''
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = (res) => {
      resizeBase64Img(res.target?.result as string, styleConf.max_size).then((img) => {
        setBase64Info({
          ...img,
          source: res.target?.result as string,
          timestamp: dayjs().unix(),
          generate: true,
          pass: false
        })
      })
    }
  }

  // app选择图片
  const citongImg = () => {
    SchemaToApp('miaobiai://art?type=fun&method=selectAPhoto&isCrop=true', (res) => {
      if (!res.data) {
        Taro.showToast({
          title: `请重新选择图片`,
          icon: 'none'
        })
        return
      }
      if (res.data.indexOf(';base64,') == -1) {
        // 兼容处理，安卓获取的图片base64码没有前缀，而苹果也没有,base64前缀并不固定
        res.data = 'data:image/jpeg;base64,' + res.data // 加上base64前缀
      }
      console.log('app选择图片', res.data)
      resizeBase64Img(res.data, styleConf.max_size).then((img) => {
        setBase64Info({
          ...img,
          source: res.data,
          timestamp: dayjs().unix(),
          generate: true,
          pass: false
        })
      })
    })
  }

  // 描述词输入框聚焦
  const descriptionInputFocus = () => {
    // setTimeout(() => {
    descriptionInput.current?.focus()
    // }, 500);
  }

  const descriptionItemInputFocus = (index) => {
    console.log(document.getElementById(`input${index}`), index)
    document.getElementById(`input${index}`)?.focus()
  }

  const inputChange = (e) => {
    if (description.join('').length + e.target.value.length <= 200) {
      setInputText(e.target.value)
    } else {
      Taro.showToast({
        title: `仅支持输入200字`,
        icon: 'none'
      })
    }
  }

  // 描述词添加
  const descriptionAdd = (e, type = 'onKeyDown') => {
    console.log(e.keyCode, e)
    // 按下回车
    if (e.keyCode === 13 || type === 'onBlur') {
      if (e.target.value.trim().length + description.join('').length > 200) {
        // e.stopPropagation();
        return
      }
      if (e.target.value.trim() === '') {
        if (changeIndex !== -1) {
          setChangeIndex(-1)
          descriptionInputFocus()
          if (type !== 'onBlur') {
            setDescription((v) => {
              v.splice(changeIndex, 1)
              console.log(v)
              return v
            })
          }
        }
        return
      }
      setInputText('')
      if (changeIndex !== -1) {
        setDescription((v) => {
          v[changeIndex] = e.target.value.trim()
          return v
        })
      } else {
        setDescription([...description, ...e.target.value.trim().split(',')])
      }
      setChangeIndex(-1)
      descriptionInputFocus()
    }
    // 按下退格删除
    if (e.keyCode === 8) {
      if (e.target.value.trim() === '') {
        setDescription(description.slice(0, description.length - 1))
        setChangeIndex(-1)
      }
    }
  }

  // 删除描述词
  const delDescription = (e: MouseEvent<HTMLImageElement>, i: number) => {
    e.stopPropagation()
    setDescription(description.filter((_, index) => index !== i))
  }

  // 生成
  const [generateState, generateFetch] = useAsyncFn(async () => {
    const data = formData || {}
    const extra: Record<string, any> = {
      enable_hr,
      image_rst: 'oss',
      save_images: true,
      script_args: [],
      send_images: true,
      styles: [],
      prompt: `${data?.prompt || ''}@${conf.isShowDescWord ? description.join(',') : ''}`
    }
    if (data.toolsType === imageType.img2img) {
      extra.init_images = [base64Info.base64]
      // 高清修复
      if (enable_hr) {
        // extra.hr_upscaler = "ESRGAN_4x";
        // extra.denoising_strength = 0.01;
        // extra.hr_resize_x = base64Info.width * 1.5;
        // extra.hr_resize_y = base64Info.height * 1.5;
      }
    }
    if (data.toolsType === imageType.txt2img) {
      const alwayson_scripts = data.alwayson_scripts
      alwayson_scripts.controlnet.args.forEach((v) => {
        v.image = base64Info.base64
        v.enabled = !!v.image
      })
      extra.alwayson_scripts = alwayson_scripts
      extra.width = base64Info.width
      extra.height = base64Info.height
      // 高清修复
      if (enable_hr) {
        // extra.hr_upscaler = "ESRGAN_4x";
        // extra.denoising_strength = 0.01;
        // extra.hr_resize_x = base64Info.width * 1.5;
        // extra.hr_resize_y = base64Info.height * 1.5;
      }
    }
    if (data.toolsType === imageType.extras) {
    }

    const res = await aiAvatarCreate({
      bizParam: {
        uid: user.id,
        toolId: router.params.toolId,
        styleName: style
      },
      configParam: {
        ...data,
        ...extra
      }
    })
    console.log(res)
    if (res.code !== 200) {
      return []
    }
    SchemaToApp(
      `miaobiai://art?type=fun&method=reloadData&json=${JSON.stringify({
        source: 'AICreateList'
      })}`,
      () => {}
    )
    Taro.navigateTo({
      url: `/pages/ai-job-detail/index?jobId=${res.payload}&toolsType=${formData.toolsType}&toolId=${router.params.toolId}&from=ai-job`
    })
    return
  }, [formData, style, description, base64Info, enable_hr])

  return (
    <div className="w-full h-full" style={{ backgroundColor: conf.bgColor, fontFamily: 'HYZhengYuan-55W' }}>
      {/* 背景 */}
      <img className="absolute w-[143px] h-[281px] md:w-[11.726vw] md:h-[23.042vw] right-0" src={bj1Img} />
      <img className="absolute w-[257px] h-[212px] top-[612px] md:w-[21.074vw] md:h-[17.384vw] md:top-[50.184vw]" src={bj2Img} />
      <img className="absolute w-[305px] h-[197px] right-0 bottom-[156px] md:w-[25.01vw] md:h-[16.154vw] md:right-0 md:bottom-[12.7vw]" src={bj3Img} />
      <div className="w-full h-full flex flex-col">
        <div className="flex-1 w-full overflow-auto pt-[38px] md:pt-[1.69vw] md:flex md:flex-col md:justify-evenly">
          {/* 图 */}
          <div className="relative mx-auto w-[674px] h-[685px] rounded-[87px] md:w-[47.66vw] md:m-auto md:h-[48.24vw] md:rounded-[6.15vw] overflow-hidden">
            <img className="w-full h-full absolute z-0" src={conf.pictureBg} />
            <div className="relative overflow-hidden w-full h-[674px] rounded-[80px] md:h-[47.66vw] md:rounded-[7.81vw] flex items-center">
              <ProgressLoading time={2000} state={chooseImgState.loading} />
              {chooseImgState.loading ? null : base64Info.base64 && base64Info.pass ? (
                <img onClick={chooseImage} className="w-full h-full object-contain rounded-[30px] md:rounded-[2.12vw]" src={base64Info.source} />
              ) : (
                <div onClick={chooseImage} className="w-full h-full flex_center flex-col anim_btn">
                  <img className="w-[74px] h-[74px] mb-[35px] md:w-[5.27vw] md:h-[5.21vw] md:mb-[2.47vw]" src={add} />
                  <div className="font-normal text-[33px] md:text-[2.34vw] text-[#B4BFD9]">点击上传参考图</div>
                </div>
              )}
              <input onChange={fileChange} type="file" ref={fileInput} accept="image/*" hidden />
            </div>
          </div>

          {conf.isShowDescWord ? (
            <>
              <div className="font-normal text-black text-[33px] my-[28px] w-[674px] md:text-[2.74vw] md:my-[0.97vw] md:w-[93.55vw] mx-auto">描述词：</div>

              <div
                className="min-h-min md:min-h-[12.92vw] mx-auto relative rounded-[20px] w-[674px] pb-[80px] md:rounded-[3.91vw] md:w-[93.55vw] md:pb-[7.25vw]"
                style={{ backgroundColor: conf.darkColor }}
                // onClick={descriptionInputFocus}
              >
                {/* 描述词 */}
                <div className="flex flex-wrap p-[26px] min-h-[44px] md:p-[2.34vw] md:min-h-[4.11vw] md:max-h-[13vw] md:overflow-auto">
                  {description.map((item, index) => (
                    <div key={index} className="bg-white inline-block min-h-[44px] rounded-[22px] mr-[15px] mb-[15px] md:min-h-[4.11vw] md:rounded-[1.81vw] md:mr-[1.23vw] md:mb-[1.23vw]">
                      <div className="w-full h-full flex_center">
                        <div
                          onClick={() => {
                            setChangeIndex(index)
                            setInputText(item)
                            descriptionItemInputFocus(index)
                          }}
                          className={`${changeIndex === index ? 'hidden' : ''} font-normal text-black text-[24px] ml-[20px] mr-[12px] md:text-[2.47vw] md:ml-[1.64vw] md:mr-[0.82vw]`}
                        >
                          {item}
                        </div>
                        <input
                          id={`input${index}`}
                          onKeyDown={descriptionAdd}
                          onChange={inputChange}
                          value={inputText}
                          onBlur={(e) => descriptionAdd(e, 'onBlur')}
                          placeholder="请输入描述词"
                          className={`${
                            changeIndex === index ? 'w-full' : 'w-0'
                          } select-text bg-transparent indent-[15px] h-[44px] text-[24px] md:indent-[1.23vw] md:h-[4.11vw] md:text-[2.47vw] border-0 focus-visible:outline-none focus:outline-none`}
                        />
                        <img onClick={(e) => delDescription(e, index)} className="w-[27px] h-[27px] mr-[10px] md:w-[2.28vw] md:h-[2.28vw] md:mr-[0.82vw] anim_btn" src={closeImg} />
                      </div>
                    </div>
                  ))}
                  <div className="flex-1 min-w-[100px] h-[44px] md:min-w-[7.09vw] md:h-[4.11vw] flex">
                    <input
                      ref={descriptionInput}
                      onKeyDown={descriptionAdd}
                      onChange={inputChange}
                      value={inputText}
                      onBlur={(e) => descriptionAdd(e, 'onBlur')}
                      placeholder="请输入描述词"
                      className={`${
                        changeIndex === -1 ? 'w-full' : 'w-0'
                      } select-text bg-transparent placeholder:text-[#FFF] h-[44px] text-[24px] md:h-[4.11vw]  md:text-[2.47vw] text-black border-0 focus-visible:outline-none focus:outline-none`}
                    />
                  </div>
                </div>

                {/* 字数 */}
                <div className="absolute bottom-[25px] left-[26px] text-[24px] md:bottom-[1.767vw] md:left-[1.838vw] md:text-[1.97vw] font-medium text-white">
                  {description.join('').length + inputText.length}/200
                </div>

                {/* 清空 */}
                <div
                  onClick={(e) => {
                    e.stopPropagation()
                    setDescription([])
                  }}
                  className="absolute bottom-[9px] right-[134px] w-[87px] h-[87px] md:bottom-[0.72vw] md:right-[10.87vw] md:w-[7.36vw] md:h-[7.29vw] rounded-full flex_center anim_btn"
                >
                  <img className="w-full h-full" src={trashcanImg} />
                </div>

                {/* 录音弹窗 */}
                <AnimatePresence>
                  {canRecording ? (
                    <>
                      <motion.div
                        {...maskAnimate}
                        onClick={(e) => {
                          // if (versionInfo.versionNumber >= 40508) {
                          e.stopPropagation()
                          e.preventDefault()
                          // }
                        }}
                        className="fixed bg-[#00000050] w-full h-full top-0 left-0 z-[701]"
                      ></motion.div>
                      <motion.div
                        {...defAnimate}
                        className="rounded-[20px] w-[719px] h-[269px] bottom-[112px] left-[-23px] 
                        md:rounded-none md:w-[70.25vw] md:h-[26.3vw] md:bottom-[3.2vw] md:left-[11.65vw]
                        flex_center absolute z-[702]"
                      >
                        <img className="w-full h-full absolute z-0" src={modal3Img} />
                        <img className="w-[541px] h-[48px] mt-[55px] md:w-[52.858vw] md:h-[4.69vw] md:mt-[5.37vw] relative" src={waveGif} />
                        {/* <div className="absolute bottom-[-36px] right-[36px] w-0 h-0 border-b-[#FFFFFF] border-[20px] border-solid border-transparent rotate-180"></div> */}
                      </motion.div>{' '}
                    </>
                  ) : null}
                </AnimatePresence>
                {/* 录音按钮 */}
                <Spin
                  // bgColor="transparent"
                  state={voiceState.loading}
                  onTouchStart={(e) => {
                    // if (versionInfo.versionNumber < 40508) {
                    //   e.preventDefault()
                    //   e.stopPropagation()
                    //   console.log('开始录音 onTouchStart', canRecording)
                    //   if (!voiceState.loading) {
                    //     // 开始录音
                    //     microphoneClick(true)
                    //   }
                    // }
                  }}
                  onTouchEnd={(e) => {
                    // if (versionInfo.versionNumber < 40508) {
                    //   e.preventDefault()
                    //   e.stopPropagation()
                    //   console.log('结束录音 onTouchEnd', canRecording)
                    //   if (!voiceState.loading) {
                    //     // 结束录音
                    //     setTimeout(() => microphoneClick(false), 200)
                    //   }
                    // }
                  }}
                  onClick={(e) => {
                    if (!canRecording) {
                      console.log('录音 onClick')
                      e.stopPropagation()
                      e.preventDefault()
                      // 开始录音
                      microphoneClick(true)
                    }
                  }}
                  ignoreClick={(e) => {
                    e.stopPropagation()
                    e.preventDefault()
                  }}
                  className={`absolute bottom-[9px] right-[13px] w-[87px] h-[87px] md:bottom-[0.72vw] md:right-[1.04vw] md:w-[7.36vw] md:h-[7.29vw] rounded-full flex_center anim_btn ${
                    canRecording ? 'z-[702]' : ''
                  }`}
                >
                  <img className="w-full h-full touch_callout pointer-events-none" src={canRecording ? microphoneActiveImg : microphoneImg} />
                </Spin>
              </div>
            </>
          ) : null}

          <div className="font-normal text-black text-[33px] my-[28px] w-[674px] md:text-[2.74vw] md:my-[0.97vw] md:w-[93.55vw] mx-auto">风格：</div>

          <div className="mx-auto relative flex flex-wrap w-[674px] gap-[6px] md:w-[93.55vw] md:gap-[2.9625vw]">
            {jsonState.value?.style.map((item, index) => (
              <div
                key={index}
                onClick={() => setStyle(item.formData.name)}
                className="mb-[6px] w-[130px] h-[165px] rounded-[27px]
                md:mb-[0.594vw] md:w-[16.34vw] md:h-[20.7vw] md:rounded-[2.673vw]
                inline-block anim_btn relative"
              >
                <img className="w-full h-full absolute z-0" src={style === item.formData.name ? styleCheckImg : styleImg} />
                <div className="w-full h-full flex items-center flex-col relative">
                  <div className="relative mt-[10px] w-[110px] h-[110px] md:mt-[1.2vw] md:w-[13.826vw] md:h-[13.826vw]">
                    <ImgPlaceholder width="80%" className="w-full h-full rounded-[20px] md:rounded-[2.52vw]" src={item.formData.icon} />
                  </div>
                  <div className={`font-normal text-center text-[18px] mt-[6px] md:text-[1.782vw] md:mt-[1.3vw] leading-1 ${style === item.formData.name ? 'text-white' : 'text-black'}`}>
                    {item.formData.name}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 高清修复 */}
          {styleConf.is_show_hr ? (
            <div className="w-[674px] md:w-[93.55vw] mx-auto h-[46px] md:h-[3.82vw] flex justify-between relative mt-[25px] md:mt-[1.075vw]">
              <div className="font-normal text-[33px] leading-[46px] md:text-[2.74vw] md:leading-[3.82vw] text-black text-left not-italic normal-case">高清修复：</div>
              <div className="w-[67px] h-[46px] md:w-[5.563vw] md:h-[3.82vw] text-[0]" onClick={() => setEnable_hr((v) => !v)}>
                <img className="w-[67px] h-[46px] md:w-[5.563vw] md:h-[3.82vw]" src={enable_hr ? switchOnImg : switchOffImg} />
              </div>
            </div>
          ) : null}
        </div>

        {/* 生成 */}
        <div className="w-full mx-auto h-[156px] md:h-[12.7vw] z-50 flex_center" style={{ backgroundColor: conf.bgColor }}>
          <div
            onClick={base64Info.base64 && base64Info.pass ? generateFetch : () => {}}
            className={`mx-auto w-[674px] h-[102px] 
              md:w-[93.62vw] md:h-[8.53vw] 
            font-medium text-white flex_center ${base64Info.base64 && base64Info.pass ? 'anim_btn' : ''}`}
          >
            <img className="w-full h-full block md:hidden" src={base64Info.base64 && base64Info.pass ? generated : generatedGreyImg}></img>
            <img className="w-full h-full hidden md:block" src={base64Info.base64 && base64Info.pass ? generatedMax : generatedGreyMaxImg}></img>
          </div>
        </div>
      </div>

      {/* 生成Loading */}
      <MaskLoading state={generateState.loading} time={2000} />
    </div>
  )
}

export default WithDataFetching(Index)
