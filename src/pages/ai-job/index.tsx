import { useRouter } from '@tarojs/taro'
import './index.less'
import Txt2img from './components/Txt2img'
import Pic2dance from './components/Pic2dance'
import AiStory from './components/AiStory'

export default function Index() {
  const router = useRouter()
  return (
    <>
      {router.params.toolsType === 'txt2img' ? <Txt2img /> : null}
      {router.params.toolsType === 'pic2dance' ? <Pic2dance /> : null}
      {router.params.toolsType === 'ai_story' ? <AiStory /> : null}
    </>
  )
}
