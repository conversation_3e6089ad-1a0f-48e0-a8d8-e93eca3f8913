import { useRouter } from '@tarojs/taro'
import './index.less'
import Txt2img from './components/Txt2img'
import AiStory from './components/AiStory'

export default function Index() {
  const router = useRouter()
  return (
    <>
      {/* 头像、场景、舞蹈、绘画、积木、建筑、化龙 */}
      {/* {router.params.toolsType === "txt2img" ||
      router.params.toolsType === "pic2dance" ||
      router.params.toolsType === "pic2img" ? (
        <Txt2img />
      ) : null} */}
      {/* 百科、绘本、故事汇、寓言故事 */}
      {/* {router.params.toolsType === "ai_story" ? <AiStory /> : null} */}
      <AiStory />
    </>
  )
}
