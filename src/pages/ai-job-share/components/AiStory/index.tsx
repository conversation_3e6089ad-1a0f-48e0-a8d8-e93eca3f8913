import palyImg from '@/assets/images/ai-dance/play.png'
import bj1Img from '@/assets/images/ai-box/bj1.png'
import bj2Img from '@/assets/images/ai-box/bj2.png'
import bj3Img from '@/assets/images/ai-box/bj3.png'
import logoImg from '@/assets/images/ai-story/logo.png'
import codeImg from '@/assets/images/ai-story/code.png'
import { useDidHide, useDidShow, useRouter } from '@tarojs/taro'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useAsyncFn, useToggle } from 'react-use'
import { defAnimate, maskAnimate } from '@/components/Modal'
import { AnimatePresence, motion } from 'framer-motion'
import { aiStoryDetail, aiWikiShare } from '@/api/ai-story'
import SwiperCore from '@msb-next/swiper'
import { Swiper, SwiperSlide } from '@msb-next/swiper/react'
import '@msb-next/swiper/swiper-bundle.min.css'
import useGetState from '@/hooks/useGetState'
import ImgPlaceholder from '@/components/ImgPlaceholder'
import { useAtomValue } from 'jotai'
import { boxConfState } from '@/store/global'
import WithDataFetching from '@/components/WithDataFetching'

interface AudioItem {
  id: string
  type: number
  imageUrl: string
  readText: string
  text: string
  audioUrl: string
}

interface WorkData {
  workId: string
  worksType: number
  showType: string
  backgroundMusicUrl: string
  isCollection: boolean
  collectionTitle: string
  collectionId: string
  imageList: AudioItem[]
}

interface Detail {
  id: string
  title: string
  workData: WorkData
}

function Index() {
  const router = useRouter()
  const [play, setPlay] = useToggle(false)
  const conf = useAtomValue(boxConfState)[router.params.toolId || '1']
  const bgAudioRef = useRef<HTMLAudioElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const [audioIndex, setAudioIndex, getAudioIndex] = useGetState(0) // 音频播放下标
  const [audioList, setAudioList, getAudioList] = useGetState<AudioItem[]>([]) // 音频列表
  const [mainSwiper, setMainSwiper] = useState<SwiperCore | null>(null)
  const [bgReady, setBgReady] = useToggle(false)

  useDidShow(() => {
    if (audioRef.current) {
      audioRef.current.muted = false
    }
    if (bgAudioRef.current) {
      bgAudioRef.current.muted = false
    }
  })

  useDidHide(() => {
    if (audioRef.current) {
      const audio = audioRef.current
      audio.muted = true
      setTimeout(() => {
        audio.pause()
      }, 500)
    }
    if (bgAudioRef.current) {
      const bgAudio = bgAudioRef.current
      bgAudio.muted = true
      setTimeout(() => {
        bgAudio.pause()
      }, 500)
    }
  })

  useEffect(() => {
    const audio = audioRef.current
    const bgAudio = bgAudioRef.current
    if (audio) {
      audio.addEventListener('play', () => onPlay(true))
      audio.addEventListener('pause', () => onPlay(false))
      requestAnimationFrame(updateProgressBar)
    }
    return () => {
      if (audio) {
        // 清除事件监听器
        audio.removeEventListener('play', onPlay)
        audio.removeEventListener('pause', onPlay)
        audio.pause()
      }
      if (bgAudio) {
        bgAudio.pause()
      }
    }
  }, [])

  // 音频播放完毕自动下标指向下一个
  useEffect(() => {
    audioRef.current!.onended = () => {
      const index = getAudioIndex()
      const list = getAudioList()
      // 最后一个
      if (index === list.length - 1) {
        setAudioIndex(0)
      } else {
        setAudioIndex((v) => v + 1)
      }
    }
    audioRef.current!.oncanplay = () => {
      console.log('oncanplay', audioRef.current)
      audioRef.current?.play().catch(() => {})
    }
    bgAudioRef.current!.oncanplay = () => {
      setBgReady(true)
    }
  }, [])

  // 控制背景音乐的播放
  useEffect(() => {
    if (play) {
      if (bgReady) {
        bgAudioRef.current?.play().catch(() => {})
      }
    } else {
      setTimeout(() => {
        if (getAudioIndex() !== getAudioList().length - 1 && audioRef.current?.paused) {
          bgAudioRef.current?.pause()
        }
      }, 100)
    }
  }, [bgReady, play])

  // 根据下标逐步播放音频
  useEffect(() => {
    if (audioRef.current && audioList.length) {
      console.log('播放', audioIndex, audioList[audioIndex])
      if (audioList.length && audioList[audioIndex] && audioList[audioIndex].audioUrl) {
        audioRef.current.src = audioList[audioIndex].audioUrl
        audioRef.current?.play().catch(() => {})
      } else {
        audioRef.current.pause()
      }
    }
    mainSwiper?.slideTo(audioIndex + 1)
  }, [audioList, audioIndex])

  // 在音频加载完元数据时获取音频时长
  const onPlay = useCallback((isPlay) => {
    setPlay(isPlay)
  }, [])

  // 更新进度条
  const updateProgressBar = () => {
    if (audioRef.current) {
      const progress = (audioRef.current.currentTime / audioRef.current.duration) * 100
      // 设置progressBarRef.current子节点的宽度
      const audioIndexCurrent = getAudioIndex()
      const progressBarList = document.querySelectorAll<HTMLElement>('.progressBar')
      progressBarList.forEach((item, index) => {
        item!.style.width = audioIndexCurrent <= index ? '0%' : '100%'
        if (audioIndexCurrent > index) {
          item!.style.width = `100%`
        } else if (audioIndexCurrent === index) {
          if (audioIndexCurrent === progressBarList.length - 1) {
            item!.style.width = `100%`
          } else {
            item!.style.width = `${progress}%`
          }
        } else {
          item!.style.width = '0%'
        }
      })
      requestAnimationFrame(updateProgressBar)
    }
  }

  // 获取详情
  const [detailState, detailFetch] = useAsyncFn<() => Promise<Detail | null>>(async () => {
    const res = conf.isWiki ? await aiWikiShare(router.params.jobId) : await aiStoryDetail({ id: router.params.jobId, isShare: 1 })
    if (res.code !== 200) {
      return null
    }
    const data = conf.isWiki ? res.payload : res.payload.storyContent.wikiVo
    setAudioList(data.workData.imageList || [])
    return data
  }, [])

  // 第一次获取详情
  useEffect(() => {
    detailFetch()
  }, [detailFetch])

  return (
    <div className="w-full mx-auto select-none min-h-full flex flex-col" style={{ backgroundColor: conf.bgColor, fontFamily: 'HYZhengYuan-55W' }}>
      <img className="absolute w-[143px] h-[281px] md:w-[11.726vw] md:h-[23.042vw] right-0" src={bj1Img} />
      <img className="absolute w-[257px] h-[212px] top-[612px] md:w-[21.074vw] md:h-[17.384vw] md:top-[50.184vw]" src={bj2Img} />
      <img className="absolute w-[305px] h-[197px] right-0 bottom-[164px] md:w-[25.01vw] md:h-[16.154vw] md:right-0 md:bottom-[12.7vw]" src={bj3Img} />
      {/* 背景 */}
      <audio preload="auto" ref={bgAudioRef} />

      {/* 绘本声音 */}
      <audio preload="auto" ref={audioRef} />

      {/* 展示 */}
      <div className="w-[670px] h-[1261px] py-[35px] md:py-[2.11vw] md:w-[56.61vw] md:h-[105.06vw] m-auto overflow-hidden">
        <div className="relative rounded-[40px] w-full h-full my-auto overflow-hidden" style={{ backgroundColor: conf.darkColor }}>
          <div className="w-full h-full relative flex">
            {/* 正常 */}
            {audioList.length ? (
              <>
                <Swiper
                  loop
                  className="w-full"
                  onSwiper={(e) => setMainSwiper(e)}
                  onSlideChange={(swiper) => {
                    setAudioIndex(swiper.realIndex)
                  }}
                >
                  {audioList.map((item, index) => (
                    <SwiperSlide key={index}>
                      <div
                        className="w-full h-full flex_center"
                        onClick={() => {
                          audioRef.current?.pause()
                        }}
                      >
                        <ImgPlaceholder className="w-full h-full object-cover aa" src={item.imageUrl} />
                        {index === 0 ? (
                          <div className="mt-[241px] md:mt-[20.5vw] w-full absolute top-0 flex flex-col items-center">
                            <div className="w-full h-[67px] md:h-[5.67vw] flex justify-end">
                              <img className="w-[357px] h-[67px] mr-[50px] md:w-[30.03vw] md:h-[5.67vw] md:mr-[5vw]" src={conf.tag} alt="" />
                            </div>
                            <div
                              className="mx-[50px] mt-[10px] text-[77px] md:mx-[5.2vw] md:mt-[0.82vw] md:text-[6.44vw] text-white leading-[100px] text-center not-italic normal-case relative z-0 ai_story_stroke"
                              data-content={detailState.value?.title}
                            >
                              {detailState.value?.title}
                            </div>
                          </div>
                        ) : index === audioList.length - 1 ? (
                          <div className="mt-[281px] md:mt-[15.9vw] w-full absolute top-0 flex flex-col items-center">
                            <img className="w-[325px] h-[57px] md:w-[27.15vw] md:h-[4.62vw]" src={logoImg} alt="" />
                            <img className="w-[200px] h-[200px] mt-[439px] md:w-[16.92vw] md:h-[16.95vw] md:mt-[44.84vw]" src={codeImg} alt="" />
                            <div className="font-semibold text-[24px] mt-[20px] md:text-[2.02vw] md:mt-[1.13vw] text-white leading-[22px] text-center not-italic normal-case">扫码下载小熊美术App</div>
                          </div>
                        ) : (
                          <div className="absolute bottom-[118px] mx-auto w-[550px] p-[30px] text-[26px] rounded-[13px] leading-[41px] md:bottom-[10.35vw] md:mx-auto md:w-[46.97vw] md:p-[2.41vw] md:text-[2.132vw] md:rounded-[1.066vw] md:leading-[3.362vw] bg-[#00000035] font-normal text-white not-italic normal-case">
                            {item.text}
                            <img className="absolute w-[322px] h-[61px] top-[-61px] md:w-[27.04vw] md:h-[5.12vw] md:top-[-5.12vw] right-0" src={conf.tag} alt="" />
                          </div>
                        )}
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                <AnimatePresence>
                  {!play && audioList[audioIndex].audioUrl ? (
                    <motion.div {...maskAnimate} className="absolute z-10 w-full h-full flex_center pointer-events-none">
                      <motion.img
                        {...defAnimate}
                        onClick={() => {
                          audioRef.current?.play().catch(() => {})
                        }}
                        className="w-[151px] h-[152px] md:w-[12.76vw] md:h-[12.76vw] pointer-events-auto"
                        src={palyImg}
                      />
                    </motion.div>
                  ) : null}
                </AnimatePresence>

                <div className="absolute z-10 bottom-[85px] left-[30px] w-[610px] h-[8px] gap-[20px] md:bottom-[7.68vw] md:left-[2.41vw] md:w-[51.79vw] md:h-[0.4vw] md:gap-[1.64vw] flex justify-between items-center">
                  {audioList.map((item, index) => (
                    <div key={index} className="flex-1 h-full rounded-full overflow-hidden bg-black">
                      <div id={`progressBar_${index}`} className="progressBar bg-white h-full"></div>
                    </div>
                  ))}
                </div>
              </>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  )
}

export default WithDataFetching(Index)
