import WithDataFetching from '@/components/WithDataFetching'
import { boxConfState } from '@/store/global'
import { useRouter } from '@tarojs/taro'
import { useAtomValue } from 'jotai'

function Index() {
  const router = useRouter()
  const conf = useAtomValue(boxConfState)[router.params.toolId || '1']

  return (
    <div className="w-full h-full" style={{ backgroundColor: conf.bgColor, fontFamily: 'HYZhengYuan-55W' }}>
      AI
    </div>
  )
}

export default WithDataFetching(Index)