import React, { useEffect, useRef, useState } from 'react'
import { useInterval } from 'react-use'
import loadingGif from '@/assets/images/ai-box/loading2.gif'
import Modal from '../Modal'

interface Props {
  state?: boolean
  scale?: number
  time: number
}

export default function Index({ state = true, time = 2000, scale = 95, ...restProps }: Props) {
  const tRef = useRef<NodeJS.Timeout | null>(null)
  const [progress, setProgress] = useState(0)
  const [progressControl, setProgressControl] = useState({
    isStart: false, // 是否已经开始
    runnig: false, // 是否运行
    speed: 200 // 步进一次的时间
  })

  useEffect(() => {
    if (!state) {
      setProgressControl({
        isStart: false,
        runnig: false,
        speed: 200
      })
      setProgress(100)
    } else {
      setProgress(0)
      setProgressControl({
        isStart: true,
        runnig: true,
        speed: time / scale
      })
      tRef.current = setTimeout(() => {
        setProgressControl((v) => ({
          ...v,
          runnig: false
        }))
      }, time)
    }
    return () => {
      tRef.current && clearTimeout(tRef.current)
    }
  }, [state])

  // 假进度
  useInterval(
    () => {
      progress < 100 && setProgress((v) => v + 1)
    },
    progressControl.runnig && progress < 100 ? progressControl.speed : null
  )

  return (
    <Modal show={state}>
      <div className="w-full h-full flex_center flex-col z-10 font-normal" {...restProps}>
        <div className="bg-[#00000045] rounded-full flex_center flex-col w-[259px] h-[259px] md:w-[21.22vw] md:h-[21.22vw]">
          <img className="w-[110px] h-[110px] mb-[20px] md:w-[8.4vw] md:h-[8.4vw]" src={loadingGif} />
          <div className="font-medium text-[40px] md:text-[3.28vw] text-[#D95100] leading-[1] ai_progress_num_stroke relative" data-content={`${progress}%`}>
            {progress}%
          </div>
        </div>
      </div>
    </Modal>
  )
}
