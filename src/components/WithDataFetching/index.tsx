import React, { useEffect, useRef, useState } from 'react'
import MaskLoading from '../MaskLoading'
import { useAtom, useSetAtom } from 'jotai'
import { boxConfState } from '@/store/global'
import { useAsyncFn } from 'react-use'
import { getHundredBoxConfig } from '@/api/global'

export default (WrappedComponent) => {
  return (props) => {
    const [boxConf, setBoxConf] = useAtom(boxConfState)

    // 生成
    const [generateState, generateFetch] = useAsyncFn(async () => {
      const res = await getHundredBoxConfig()
      setBoxConf(res.payload)
      return
    }, [])

    useEffect(() => {
      if (Object.keys(boxConf).length === 0) {
        generateFetch()
      }
    }, [])

    if (generateState.loading || Object.keys(boxConf).length === 0) {
      return <MaskLoading state={generateState.loading} time={5000} />
    }

    return <WrappedComponent />
  }
}
