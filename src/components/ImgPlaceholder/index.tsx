import React, { useState } from 'react'
import bitmapImg from '@/assets/images/ai-dance/bitmap.png'
import PicError from '@/assets/images/ai-box/fail2.png'

interface IProps {
  style?: React.CSSProperties
  width?: string
  onLoad?: () => void
  [key: string]: any
}

export default ({ style = {}, onLoad = () => {}, width = '40%', ...props }: IProps) => {
  const { display = '' } = style
  const [state, setState] = useState(false)
  const [errorState, setErrorState] = useState(false)
  const load = () => {
    setState(true)
    onLoad()
  }

  const error = () => {
    setErrorState(true)
    onLoad()
  }

  return (
    <React.Fragment>
      <img {...props} style={{ ...style, display: state ? display || '' : 'none' }} onLoad={load} onError={error} />
      {!state && !errorState ? <img {...props} style={{ ...style, margin: '0 auto', width: 'auto', display }} src={bitmapImg} alt="ImgPlaceholder" /> : null}
      {errorState ? (
        <div className="w-full h-full flex_center flex-col absolute top-0 z-10">
          <img style={{ margin: '0 auto', width: width, display }} src={PicError} alt="ImgPlaceholder" />
        </div>
      ) : null}
    </React.Fragment>
  )
}
