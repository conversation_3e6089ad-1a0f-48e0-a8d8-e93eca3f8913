import { atom } from 'jotai'

export interface Userinfo {
  head: string
  id: number
  mobile: null | string
  name: string
  openid: string
  session_key: string
  token: string
}

interface confItem {
  // 背景颜色
  bgColor: string
  // 标签图片
  tag: string
  // 工具名称
  toolName: string
  // 图片背景
  pictureBg: string
  // 深色背景
  darkColor: string
  // 原生路径
  path?: string
  // 工具类型
  toolsType: 'txt2img' | 'pic2dance' | 'pic2img' | 'ai_story' | 'Unknown'
  // ai_story 是否是百科类型
  isWiki?: boolean
  // 是否显示描述文字，txt2img类型独有
  isShowDescWord?: boolean
  // 故事类型 book 绘本, fable 寓言,story 故事汇
  type?: 'book' | 'fable' | 'story'
  // 是否显示相机按钮
  hasCamera?: boolean
  
}

type BoxConf = Record<string, confItem>

export const activeIndexState = atom('/pages/index/index')
export const showTabBarState = atom(true)
export const userinfoState = atom<null | Userinfo>(null)
export const boxConfState = atom<BoxConf>({})
