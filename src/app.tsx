import { PropsWithChildren, useCallback, useEffect, useRef } from 'react'
import Taro, { eventCenter, useDidShow, useLaunch } from '@tarojs/taro'
import { SchemaToApp, autoLogin, debounce, getQuery, isWx, throttle, updateWeapp, versionInfo } from './utils'
import { useSetAtom } from 'jotai'
import { authorityState } from './store/authority'
import { userinfoState } from './store/global'
import eruda from 'eruda'
import './app.less'
import { refreshTxt2imgState } from './store/ai-box'

const showHomeWorkButton = debounce((isShow, toolId) => {
  SchemaToApp(`miaobiai://art?type=fun&method=showHomeWorkButton&isShow=${isShow}&toolId=${toolId}`, () => {})
}, 500)

function App({ children }: PropsWithChildren<any>) {
  const setAuthority = useSet<PERSON>tom(authorityState)
  const setUserinfo = useSetAtom(userinfoState)
  const setRefreshTxt2img = useSetAtom(refreshTxt2imgState)
  
  const refreshTxt2imgRef = useRef(
    debounce(() => {
      console.log('setRefreshTxt2img')
      setRefreshTxt2img(Math.random())
    }, 500)
  )

  const init = () => {
    console.log('当前路由', window.location)
    // toolsType=ai_story
    const path = window.location.pathname
    const toolId = getQuery('toolId')
    const toolsType = getQuery('toolsType')
    const isDance = path === '/ai-dance' || path === '/aibox/ai-dance'
    const isStory = (path === '/ai-job' || path === '/aibox/ai-job') && toolsType === 'ai_story'
    // 添加列表按钮(舞蹈和故事页面和详情)
    if (isDance || isStory) {
      showHomeWorkButton('1', toolId)
    } else {
      showHomeWorkButton('0', toolId)
    }
  }

  useEffect(() => {
    const handleRouteChange = ({ fromLocation, toLocation }) => {
      console.log('路由变化', toLocation.path)
      init()
      // 重置
      if (toLocation.path === '/ai-job' || toLocation.path === '/aibox/ai-job') {
        refreshTxt2imgRef.current()
      }
    }

    eventCenter.on('__taroRouterChange', handleRouteChange)

    return () => {
      eventCenter.off('__taroRouterChange', handleRouteChange)
    }
  }, [])

  const updateNetworkStatus = () => {
    console.log(navigator.onLine)
    if (!navigator.onLine) {
      Taro.showToast({
        title: '网络连接已断开',
        icon: 'none',
        duration: 2000
      })
    }
  }

  useEffect(() => {
    init()
    if (process.env.TARO_APP_ENV !== 'live') {
      eruda.init()
    }
    window.addEventListener('online', updateNetworkStatus)
    window.addEventListener('offline', updateNetworkStatus)

    return () => {
      window.removeEventListener('online', updateNetworkStatus)
      window.removeEventListener('offline', updateNetworkStatus)
    }
  }, [])

  useLaunch(() => {
    console.log('App launched.')
    setTimeout(() => {
      console.log('versionInfo.versionNumber', versionInfo.versionNumber)
      console.log('screen', window.screen.width, window.screen.height)
    }, 2000)
  })

  useDidShow(() => {
    if (isWx) {
      // 登陆
      autoLogin().then((res) => {
        console.log('autoLogin', res)
        if (res.code === 200) {
          // 登陆成功
          const { authority, userInfo } = res.data!
          setUserinfo(userInfo)
          setAuthority(authority)
        }
      })
      // 是否需要更新
      updateWeapp(true)
      // 网络
      Taro.onNetworkStatusChange((res) => {
        // console.log("onNetworkStatusChange", res);
        if (!res.isConnected) {
          Taro.showModal({
            title: '提示',
            content: '无法连接到互联网，请检查你的网络',
            confirmText: '确定',
            showCancel: false,
            success: (modal) => {
              if (modal.confirm) {
                console.log('用户点击确定')
              } else if (modal.cancel) {
                console.log('用户点击取消')
              }
            }
          })
        }
      })
    }
  })

  // children 是将要会渲染的页面
  return (
    <>
      <div
        className="z-[999999] fixed top-[2vw] right-[2vw] w-[1vw] h-[0.5vw]"
        onClick={() => {
          Taro.showToast({
            title: 'eruda on',
            icon: 'none'
          })
          eruda.init()
        }}
      ></div>
      {children}
    </>
  )
}

export default App
