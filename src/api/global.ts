/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-12 16:48:06
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-08-04 10:53:25
 * @FilePath: /mb-users-ai-box/src/api/global.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { isH5 } from '@/utils'
import appRequest from '@/utils/request'

const BASE_URL = isH5 ? '' : process.env.TARO_APP_API_TV_URL

// json
export const getJson = ({ url }) => {
  return appRequest.get<any>({ url })
}

// 图片转提示词接口
export const getHundredBoxConfig = () => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/v1/anon/device/getHundredBoxConfig`
  })
}
