/*
 * @Author: song<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-04 11:36:30
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-08-04 11:39:30
 * @FilePath: /mb-users-ai-box/src/api/ai-story.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { isH5 } from '@/utils'
import appRequest from '@/utils/request'

const BASE_URL = isH5 ? '' : process.env.TARO_APP_API_TV_URL

// 绘本封面
// type
export const aiStoryCover = (type) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/book/cover?type=${type}`
  })
}

// 生成第一页
// text
// type
// mainRole
export const aiStoryFirstPage = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/book/firstPage`,
    data
  })
}

// 生成其他页
// text
// bookId 绘本id
// pageNum 故事第一页传1，第二页传2
export const aiStoryoTherPage = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/book/otherPage`,
    data
  })
}

// 获取图片
// bookSdId 绘本页详情id
// bookId 绘本id
export const aiStoryBookImage = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/book/bookImage`,
    data
  })
}

// 合成绘本
// bookId 绘本id
// type
export const aiStoryCompact = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/book/compact`,
    data
  })
}

// 查看绘本详情
// id 绘本id
export const aiStoryDetail = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/book/detail/anon`,
    data
  })
}
// 图片识别
// base64 图片
// type
export const aiStoryCoverByImage = (data) => {
  return appRequest.post({
    url: `${BASE_URL}/api/tv/box100/book/getCoverByImage`,
    data
  })
}

// 分享接口
// id 绘本id
export const aiStoryShare = (id) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/book/share/info?id=${id}`
  })
}

// 百科分享
// id 绘本id
export const aiWikiShare = (id) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/aiBK/wiki/share?id=${id}`
  })
}
