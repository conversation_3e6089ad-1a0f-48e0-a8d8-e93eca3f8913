import { isH5 } from '@/utils'
import appRequest from '@/utils/request'

const BASE_URL = isH5 ? '' : process.env.TARO_APP_API_TV_URL

//
export const aiAvatarSyncBoxTool = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/syncBoxTool`,
    data
  })
}

// c端-百宝箱工具列表
export const aiAvatarToolList = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/feature/toolList`,
    data
  })
}

// h5-百宝箱工具详情
export const aiAvatarToolDetail = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/feature/toolDetail`,
    data
  })
}

// h5-音频转文字
export const aiAvatarAudio2Text = (data) => {
  return appRequest.post({
    url: `${BASE_URL}/api/tv/box100/feature/audio2Text`,
    data
  })
}

// 生成作品
export const aiAvatarCreate = (data) => {
  return appRequest.post({
    url: `${BASE_URL}/api/tv/box100/works/create`,
    data
  })
}

// ai生成回调地址
export const aiAvatarCallBack = (data) => {
  return appRequest.post({
    url: `${BASE_URL}/api/tv/box100/aiGc/callBack`,
    data
  })
}

// 作品详情
export const aiAvatarDetail = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/works/detail?t=${new Date().getTime()}`,
    data
  })
}

export const aiAvatarDetailNoToken = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/anon/works/detail?t=${new Date().getTime()}`,
    data
  })
}
// 删除作品
export const aiAvatarRemoveById = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/works/removeById`,
    data
  })
}

// 图片转提示词接口
export const aiAvatarPic2Text = (data) => {
  return appRequest.post({
    url: `${BASE_URL}/api/tv/box100/feature/pic2Text`,
    data
  })
}
