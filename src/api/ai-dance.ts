import { isH5 } from '@/utils'
import appRequest from '@/utils/request'

const BASE_URL = isH5 ? '' : process.env.TARO_APP_API_TV_URL

// ai舞蹈模型列表
export const aiDanceModeList = () => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/aiDance/modeList`
  })
}

// ai舞蹈模型-预设图
export const aiDancePrepareList = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/aiDance/prepareList`,
    data
  })
}

// 提交ai舞蹈作品
export const aiDanceSubmit = (data) => {
  return appRequest.post({
    url: `${BASE_URL}/api/tv/box100/aiDance/submit`,
    data
  })
}

// ai舞蹈模型-删除预设图
export const aiDanceRemovePic = (data) => {
  return appRequest.get({
    url: `${BASE_URL}/api/tv/box100/aiDance/removePic`,
    data
  })
}

// ai舞蹈上传
export const aiDanceUploadPic = (data) => {
  return appRequest.post({
    url: `${BASE_URL}/api/tv/box100/aiDance/uploadPic`,
    data
  })
}

// 详情使用ai头像的
