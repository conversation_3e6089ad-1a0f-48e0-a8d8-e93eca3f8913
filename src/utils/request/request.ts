import Taro from '@tarojs/taro'
import { getUser } from '..'

export interface Res {
  code: 0 | 1 | 200
  data?: any
  payload?: any
  msg?: string
  status?: string
}

const ignoreCheckUrl = ['/api/tv/box100/book/detail/anon', '/api/tv/box100/aiBK/wiki/share']

class AppRequest {
  constructor(private BASE_URL: string, private TIME_OUT: number) {}

  private interceptor(chain: Taro.Chain) {
    const requestParams = chain.requestParams
    // Taro.showLoading({
    //   title: '加载中...',
    // })
    let isIgnoreCheck = false
    ignoreCheckUrl.forEach((url) => {
      if (requestParams.url.includes(url)) {
        isIgnoreCheck = true
      }
    })
    if (!isIgnoreCheck) {
      const user = getUser()
      let token = Taro.getStorageSync('__weapp_token__') || localStorage.token || user.token // 拿到本地缓存中存的token
      if (token) {
        requestParams.header = {
          ...requestParams.header,
          Authorization: token.includes('Bearer') ? token : `Bearer ${token}`
        }
      }
    }
    return new Promise((resolve, reject) => {
      chain
        .proceed(requestParams)
        .then((res) => {
          // Taro.hideLoading()
          resolve(res)
        })
        .catch((err) => {
          // Taro.hideLoading()
          // console.error(err);
          Taro.showToast({
            title: err.message || '请求失败,请稍后再试',
            icon: 'none'
          })
          resolve({ code: 1, msg: err.message || '请求失败,请稍后再试' })
        })
    })
  }

  request<T = any>(options: Taro.request.Option) {
    // 添加拦截器
    Taro.addInterceptor(this.interceptor)
    return new Promise<T>((resolve, reject) => {
      Taro.request({
        timeout: this.TIME_OUT,
        ...options,
        success(res) {
          console.info(`%c axios `, 'color: #fff; background: #4bc729', options, res)
          if ('code' in res.data && res.data.code !== 200) {
            Taro.showToast({
              title: res.data.errors || '请求失败,请稍后再试',
              icon: 'none'
            })
          }
          resolve({ payload: res.data.data, ...res.data } as T)
        },
        fail(err) {
          reject(err)
        }
      })
    })
  }
  get<T = Res>(options: Taro.request.Option) {
    return this.request<T>({ ...options, method: 'GET' })
  }
  post<T = Res>(options: Taro.request.Option) {
    return this.request<T>({ ...options, method: 'POST' })
  }
  delete<T = Res>(options: Taro.request.Option) {
    return this.request<T>({ ...options, method: 'DELETE' })
  }
  put<T = Res>(options: Taro.request.Option) {
    return this.request<T>({ ...options, method: 'PUT' })
  }
}

export default AppRequest
