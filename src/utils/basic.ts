import tag1Img from '@/assets/images/ai-box/tag1.png'
import tag2Img from '@/assets/images/ai-box/tag2.png'
import tag3Img from '@/assets/images/ai-box/tag3.png'
import tag4Img from '@/assets/images/ai-box/tag4.png'
import tag5Img from '@/assets/images/ai-box/tag5.png'
import tag6Img from '@/assets/images/ai-box/tag6.png'
import tag7Img from '@/assets/images/ai-box/tag7.png'
import tag8Img from '@/assets/images/ai-box/tag8.png'
import tag9Img from '@/assets/images/ai-box/tag9.png'
import tag10Img from '@/assets/images/ai-box/tag10.png'
import tag11Img from '@/assets/images/ai-box/tag11.png'
import tag12Img from '@/assets/images/ai-box/tag12.png'
import tag13Img from '@/assets/images/ai-box/tag13.png'
import tag14Img from '@/assets/images/ai-box/tag14.png'
import tag15Img from '@/assets/images/ai-box/tag15.png'
import tag16Img from '@/assets/images/ai-box/tag16.png'
import tag17Img from '@/assets/images/ai-box/tag17.png'
import tag18Img from '@/assets/images/ai-box/tag18.png'
import tag19Img from '@/assets/images/ai-box/tag19.png'
import tag20Img from '@/assets/images/ai-box/tag20.png'
import tag21Img from '@/assets/images/ai-box/tag21.png'
import tag22Img from '@/assets/images/ai-box/tag22.png'
import tag23Img from '@/assets/images/ai-box/tag23.png'

import picture1Img from '@/assets/images/ai-box/picture1.png'
import picture2Img from '@/assets/images/ai-box/picture2.png'
import picture3Img from '@/assets/images/ai-box/picture3.png'
import picture5Img from '@/assets/images/ai-box/picture5.png'
import picture7Img from '@/assets/images/ai-box/picture7.png'
import picture8Img from '@/assets/images/ai-box/picture8.png'
import picture9Img from '@/assets/images/ai-box/picture9.png'
import picture11Img from '@/assets/images/ai-box/picture11.png'
import picture12Img from '@/assets/images/ai-box/picture12.png'
import picture13Img from '@/assets/images/ai-box/picture13.png'
import picture14Img from '@/assets/images/ai-box/picture14.png'
import picture15Img from '@/assets/images/ai-box/picture15.png'
// import picture16Img from "@/assets/images/ai-box/picture16.png";
import picture17Img from '@/assets/images/ai-box/picture17.png'
import picture18Img from '@/assets/images/ai-box/picture18.png'
import picture19Img from '@/assets/images/ai-box/picture19.png'
import picture20Img from '@/assets/images/ai-box/picture20.png'
import picture21Img from '@/assets/images/ai-box/picture21.png'
import picture22Img from '@/assets/images/ai-box/picture22.png'
import picture23Img from '@/assets/images/ai-box/picture23.png'

interface confItem {
  // 背景颜色
  bgColor: string
  // 标签图片
  tag: string
  // 工具名称
  toolName: string
  // 图片背景
  pictureBg: string
  // 深色背景
  darkColor: string
  // 原生路径
  path?: string
  // 工具类型
  toolsType: 'txt2img' | 'pic2dance' | 'pic2img' | 'ai_story' | 'Unknown'
  // ai_story 是否是百科类型
  isWiki?: boolean
  // 是否显示描述文字，txt2img类型独有
  isShowDescWord?: boolean
  // 故事类型 book 绘本, fable 寓言,story 故事汇
  type?: 'book' | 'fable' | 'story'
}

type BoxConf = Record<string, confItem>

export const boxConf: BoxConf = {
  '1': {
    bgColor: '#A7B7FF',
    tag: tag1Img,
    toolName: 'AI头像',
    pictureBg: picture1Img,
    darkColor: '#8099FF',
    toolsType: 'txt2img',
    isShowDescWord: true
  },
  '2': {
    bgColor: '#A7FEF1',
    tag: tag2Img,
    toolName: 'AI场景',
    pictureBg: picture2Img,
    darkColor: '#5FEBEC',
    toolsType: 'txt2img',
    isShowDescWord: true
  },
  '3': {
    bgColor: '#AFEFC5',
    tag: tag3Img,
    toolName: 'AI绘画',
    pictureBg: picture3Img,
    darkColor: '#7BD19A',
    toolsType: 'pic2img',
    path: 'miaobiai://art?type=open&page=whiteBoard'
  },
  '4': {
    bgColor: '#FFEEE6',
    tag: tag4Img,
    toolName: 'AI舞蹈',
    pictureBg: '',
    darkColor: '',
    toolsType: 'pic2dance'
  },
  '5': {
    bgColor: '#FFF1C1',
    tag: tag5Img,
    toolName: 'AI积木',
    pictureBg: picture5Img,
    darkColor: '#FFE495',
    toolsType: 'pic2img',
    path: 'miaobiai://art?type=open&page=puzzleBoard'
  },
  '6': {
    bgColor: '#B2E5FF',
    tag: tag6Img,
    toolName: 'AI百科',
    pictureBg: '',
    darkColor: '#67C9FF',
    toolsType: 'ai_story',
    isWiki: true
  },
  '7': {
    bgColor: '#C4BFF9',
    tag: tag7Img,
    toolName: 'AI化龙',
    pictureBg: picture7Img,
    darkColor: '#A4A4EB',
    toolsType: 'txt2img',
    isShowDescWord: false
  },
  '8': {
    bgColor: '#F3CDEA',
    tag: tag8Img,
    toolName: 'AI建筑',
    pictureBg: picture8Img,
    darkColor: '#EDA5D6',
    toolsType: 'pic2img',
    path: 'miaobiai://art?type=open&page=aiArchitecture'
  },
  '9': {
    bgColor: '#A7B7FF',
    tag: tag9Img,
    toolName: 'AI绘本',
    pictureBg: picture9Img,
    darkColor: '#8099FF',
    toolsType: 'ai_story',
    type: 'book'
  },
  '10': {
    bgColor: '#A7FEF1',
    tag: tag10Img,
    toolName: 'AI大师画',
    pictureBg: picture2Img,
    darkColor: '#5FEBEC',
    toolsType: 'txt2img',
    isShowDescWord: false
  },
  '11': {
    bgColor: '#FFEEE6',
    tag: tag11Img,
    toolName: 'AI中国画',
    pictureBg: picture11Img,
    darkColor: '#FFC3BB',
    toolsType: 'txt2img',
    isShowDescWord: false
  },
  '12': {
    bgColor: '#FEF1C1',
    tag: tag12Img,
    toolName: 'AI故事汇',
    pictureBg: picture12Img,
    darkColor: '#FEE494',
    toolsType: 'ai_story',
    type: 'story'
  },
  '13': {
    bgColor: '#AFEFC5',
    tag: tag13Img,
    toolName: 'AI寓言故事',
    pictureBg: picture13Img,
    darkColor: '#7CD29A',
    toolsType: 'ai_story',
    type: 'fable'
  },
  '14': {
    bgColor: '#B3E4FF',
    tag: tag14Img,
    toolName: 'AI拼图',
    pictureBg: picture14Img,
    darkColor: '#66C9FF',
    toolsType: 'pic2img',
    path: 'miaobiai://art?type=open&page=puzzleBoard'
  },
  '15': {
    bgColor: '#C4BFFA',
    tag: tag15Img,
    toolName: 'AI涂鸦',
    pictureBg: picture15Img,
    darkColor: '#A4A4EB',
    toolsType: 'pic2img',
    path: 'miaobiai://art?type=open&page=whiteBoard'
  },
  '16': {
    bgColor: '#F4CDEA',
    tag: tag16Img,
    toolName: 'AI揭秘',
    pictureBg: '',
    darkColor: '#67C9FF',
    toolsType: 'ai_story',
    isWiki: true
  },
  '17': {
    bgColor: '#A7B7FF',
    tag: tag17Img,
    toolName: 'AI装饰',
    pictureBg: picture17Img,
    darkColor: '#8099FF',
    toolsType: 'txt2img',
    isShowDescWord: false
  },
  '18': {
    bgColor: '#A7FEF1',
    tag: tag18Img,
    toolName: 'AI龙龙',
    pictureBg: picture18Img,
    darkColor: '#5FEBEC',
    toolsType: 'txt2img',
    isShowDescWord: false
  },
  '19': {
    bgColor: '#FFEEE6',
    tag: tag19Img,
    toolName: 'AI水墨',
    pictureBg: picture19Img,
    darkColor: '#FFC3BB',
    toolsType: 'txt2img',
    isShowDescWord: false
  },
  '20': {
    bgColor: '#FEF1C1',
    tag: tag20Img,
    toolName: 'AI环境',
    pictureBg: picture5Img,
    darkColor: '#FEE494',
    toolsType: 'txt2img',
    isShowDescWord: true
  },
  '21': {
    bgColor: '#AFEFC5',
    tag: tag21Img,
    toolName: 'AI装饰',
    pictureBg: picture3Img,
    darkColor: '#7CD29A',
    toolsType: 'pic2img',
    path: 'miaobiai://art?type=open&page=whiteBoard'
  },
  '22': {
    bgColor: '#B3E4FF',
    tag: tag22Img,
    toolName: 'AI房子',
    pictureBg: picture14Img,
    darkColor: '#66C9FF',
    toolsType: 'pic2img',
    path: 'miaobiai://art?type=open&page=whiteBoard'
  },
  '23': {
    bgColor: '#C4BFFA',
    tag: tag23Img,
    toolName: 'AI彩色大师',
    pictureBg: picture15Img,
    darkColor: '#A4A4EB',
    toolsType: 'txt2img',
    isShowDescWord: false
  }
}
