import Taro from '@tarojs/taro'
import appRequest from '@/utils/request'
import axios from 'axios'

// 升级 - 更新小程序 isForce 强制升级
export const updateWeapp = (isForce?: boolean): void => {
  const tipModal = (updateManager) => {
    Taro.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否马上重启小程序？',
      success: (res) => {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          try {
            Taro.clearStorageSync()
          } catch (e) {
            // Do something when catch error
          }
          updateManager.applyUpdate()
        } else {
          if (isForce) {
            tipModal(updateManager)
          }
        }
      }
    })
  }
  if (process.env.TARO_ENV === 'weapp') {
    const updateManager = Taro.getUpdateManager()
    updateManager.onCheckForUpdate((res) => {
      // 请求完新版本信息的回调
      console.log('是否有新版本: ', res.hasUpdate)
    })

    updateManager.onUpdateReady(() => {
      tipModal(updateManager)
    })

    updateManager.onUpdateFailed(() => {
      // 新的版本下载失败
      console.log('新的版本下载失败')
    })
  }
}

// 授权 - 获取用户当前的授权状态
export const checkAuthSetting = async (authSettingName) => {
  return new Promise((resolve) => {
    Taro.getSetting({
      success(res) {
        if (res.authSetting[authSettingName]) {
          resolve(true)
          console.log('授权过')
        } else {
          resolve(false)
          console.log('未授权过')
        }
      }
    })
  })
}

// 授权 - 发起授权请求
export const authSeting = async (authSettingName) => {
  return new Promise((resolve) => {
    Taro.authorize({
      scope: authSettingName,
      success() {
        resolve(true)
        console.log('授权成功')
      },
      fail() {
        resolve(false)
        console.log('授权失败')
      }
    })
  })
}

// 授权 - 打开设置界面
export const openSetting = async (authSettingName) => {
  return new Promise((resolve) => {
    Taro.openSetting({
      success(res) {
        if (res.authSetting[authSettingName]) {
          resolve(true)
        } else {
          resolve(false)
        }
        console.log('授权设置', res)
      },
      fail() {
        resolve(false)
        console.log('设置失败')
      }
    })
  })
}

// 授权 - 判断授权
export const userAPIAuthSeting = async (authSettingName, options, fn) => {
  const isAuthed = await checkAuthSetting(authSettingName)
  if (isAuthed) {
    // 授权过
    fn()
    return
  }

  const isAuthSuccess = await authSeting(authSettingName)
  if (isAuthSuccess) {
    // 授权成功
    fn()
    return
  }

  const defOptions = {
    content: '检测到您没打开相应授权，是否去设置打开？',
    confirmText: '确认',
    cancelText: '取消'
  }
  Taro.showModal({
    ...defOptions,
    ...options,
    success: async (res) => {
      if (res.confirm) {
        // 打开授权界面
        const result = await openSetting(authSettingName)
        if (result) {
          fn()
        }
      } else {
        console.log('用户点击取消')
      }
    }
  })
}

// 检查session
export const checkSession = () => {
  return new Promise((resolve) => {
    Taro.checkSession({
      success: () => {
        console.log('session 未过期')
        resolve(true)
      },
      fail: () => {
        console.log('session过期')
        resolve(false)
      }
    })
  })
}

// 登陆 - 用户登陆获取code
export const userLogin = () => {
  return new Promise((resolve) => {
    Taro.login({
      success: (res) => {
        console.log('登陆成功', res.code)
        resolve(res.code)
      },
      fail: () => {
        console.log('登录失败')
        resolve('')
      }
    })
  })
}

// 登陆 - 手机号授权 (code 0 授权成功， 1 授权失败)
export const phoneAuth = async ({ code }: { code: string }) => {
  console.log('phoneAuth', code)
  return appRequest.post({
    url: `/api/users/wxBindMobile`,
    data: { code }
  })
}

// 登陆 - 登录流
export const autoLogin = async () => {
  const code = await userLogin()
  if (code) {
    const userInfo = await appRequest.post({
      url: `/api/users/wxLogin`,
      data: { code }
    })
    console.log('userInfo', userInfo)
    if (userInfo.code === 0) {
      Taro.setStorageSync('__weapp_open_id__', userInfo.data.openid)
      Taro.setStorageSync('__weapp_token__', userInfo.data.token)
      const authority = await appRequest.get({
        url: `/api/role/findAuthority`
      })
      return {
        code: 0,
        data: { userInfo: userInfo.data, authority: authority.data }
      }
    }
    return { code: 1, msg: '用户code解析openId失败~' }
  }
  return { code: 1, msg: '用户登录获取code失败~' }
}

type Tkey =
  | 'albumAuthorized'
  | 'bluetoothAuthorized'
  | 'cameraAuthorized'
  | 'locationAuthorized'
  | 'locationReducedAccuracy'
  | 'microphoneAuthorized'
  | 'notificationAlertAuthorized'
  | 'notificationSoundAuthorized'
  | 'notificationBadgeAuthorized'
  | 'phoneCalendarAuthorized'

/**
 * 获取系统权限状态
 * 0 用户已开通对应权限
 * 1 获取系统权限失败
 * 1001 当前微信版本过低，部分功能无法使用~
 * 100 打开系统授权页成功
 * 101 打开系统授权页失败
 * 403 用户点击取消
 * 401 权限没打开
 * */
export const getSystemInfo = (keys: Tkey[], content = '') => {
  return new Promise((resolve) => {
    const canUseSta = Taro.canIUse('getAppAuthorizeSetting')
    console.log('canUseSta', canUseSta)
    if (!canUseSta) {
      return resolve({
        code: 1001,
        msg: '当前微信版本过低，部分功能无法使用~'
      })
    }

    const systemInfo = Taro.getAppAuthorizeSetting()
    console.log('system', systemInfo)
    if (!systemInfo) {
      return resolve({ code: 1, msg: '获取系统权限失败~' })
    }
    keys.forEach((item) => {
      if (systemInfo[item] !== 'authorized') {
        Taro.showModal({
          content,
          success: (res) => {
            if (res.confirm) {
              Taro.openAppAuthorizeSetting({
                success: () => {
                  return resolve({ code: 100, msg: '打开系统授权页成功~' })
                },
                fail: () => {
                  return resolve({ code: 101, msg: '打开系统授权页失败~' })
                }
              })
            } else {
              return resolve({ code: 403, msg: '用户点击取消~' })
            }
          }
        })
        return resolve({ code: 401, msg: '权限没打开~' })
      }
    })
    return resolve({ code: 0, msg: '用户已开通对应权限~' })
  })
}

// 时间格式化,默认2023/08/08 16:27:59
export const formatTime = (date, fill = '/') => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()
  return [year, month, day].map(formatNumber).join(fill) + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

// 10内数字补充0
export const formatNumber = (n) => {
  n = n.toString()
  return n[1] ? n : '0' + n
}

// 防抖
export const debounce = (func, time) => {
  let timer
  return (...args) => {
    timer && clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, time)
  }
}

// 节流
export const throttle = (callback, wait = 3000) => {
  let timer: any = null
  let startTime
  return (...args) => {
    const ctx = this
    const now = +new Date()
    if (startTime && now < startTime + wait) {
      clearTimeout(timer)
      timer = setTimeout(() => {
        startTime = now
        callback.apply(ctx, args)
      }, wait)
    } else {
      startTime = now
      callback.apply(ctx, args)
    }
  }
}

// 指定区间随机数
export const randomNum = (minNum, maxNum) => {
  return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10)
}

// 获取当前页面栈
export const path = () => {
  const pages = Taro.getCurrentPages()
  return pages[pages.length - 1]
}

// 强行睡觉,默认800
export const sleep = (time: number = 800) => {
  return new Promise((resolve) =>
    setTimeout(() => {
      resolve(true)
    }, time)
  )
}

// isH5
export const isH5 = process.env.TARO_ENV === 'h5'
// isWx
export const isWx = process.env.TARO_ENV === 'weapp'

// SchemaToApp
export const SchemaToApp = (url: string, method: (res?) => void) => {
  if (!isApp()) {
    console.log('请使用小熊美术打开', url)
    // Taro.showToast({ title: "请使用小熊美术打开", icon: "none" });
    return
  }
  window.location.href = url
  window.bear = method
}

// 浏览器类型
export const getVersion = () => {
  var u = navigator.userAgent
  return {
    // 移动终端浏览器版本信息
    trident: u.indexOf('Trident') > -1, //IE内核
    presto: u.indexOf('Presto') > -1, //opera内核
    webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
    mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或uc浏览器
    iPhone: u.indexOf('iPhone') > -1, //是否为iPhone或者QQHD浏览器
    iPad: u.indexOf('iPad') > -1, //是否iPad
    webApp: u.indexOf('Safari') == -1 //是否web应该程序，没有头部与底部
  }
}

// isApp
export const isApp = () => {
  var ua = navigator.userAgent.toLowerCase() //获取判断用的对象
  if (ua.indexOf('miaobiai') > -1 || ua.indexOf('bearmeishu') > -1) {
    return true
  }
  return false
}

export const getUser = () => {
  // console.log("localStorage.user", localStorage.user);
  if (localStorage.user) {
    return JSON.parse(localStorage.user || '{}')
  }
  console.log('localStorage.user 为空，请注入用户信息和token')
  // window.location.replace(`${process.env.TARO_APP_H5_URL}/account/login`)
  return {}
}

// img url to base64
export const convertImgToBase64 = async (url: string): Promise<string> => {
  const response = await axios({ url, responseType: 'blob' })
  const blob = response.data
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      resolve(reader.result as string)
    }
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

// get img width height
export const getImgInfo = async (url: string) => {
  const base64 = await convertImgToBase64(url)
  return new Promise((resolve) => {
    const img = new Image()
    img.src = base64
    img.onload = () => {
      resolve({ width: img.width, height: img.height })
    }
  })
}

// get base64 width height
export const getBase64Info = async (base64: string): Promise<{ width: number; height: number; img: HTMLImageElement }> => {
  return new Promise((resolve) => {
    const img = new Image()
    img.src = base64
    img.onload = () => {
      resolve({ width: img.width, height: img.height, img })
    }
  })
}

// base64 img change size, Long side=512,Short side=auto
export const resizeBase64Img = async (base64: string, maxSide: number = 512): Promise<{ width: number; height: number; base64: string }> => {
  const info = await getBase64Info(base64)
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) {
    throw new Error('Unable to get canvas context')
  }

  const ratio = info.width > info.height ? maxSide / info.width : maxSide / info.height

  canvas.width = info.width * ratio
  canvas.height = info.height * ratio

  ctx.drawImage(info.img, 0, 0, canvas.width, canvas.height)
  const base64Url = canvas.toDataURL()
  return { width: canvas.width, height: canvas.height, base64: base64Url }
}

// 延时
export const delay = (time: number = 800) => new Promise((resolve) => setTimeout(resolve, time))

// 字符串版号转数字版号，每个序号占两位,返回数字
export const versionToNumber = (version: string): number => {
  const versionArr = version.split('.')
  const v = versionArr.reduce((acc, cur) => {
    return acc + cur.padStart(2, '0')
  }, '')
  return Number(v)
}

// 获取版本信息
export const versionInfo = (() => {
  // ios ---> ArtAiClass/2.8.6/10120/38/ZH_CN/CN_AREA/2169(iPhone/13.6.1/iPhone 8 Plus)
  // android --->
  const ua = navigator.userAgent.toLowerCase()
  let infoMatch = ua.match(/miaobiai.[^(]+/gi)

  // 小熊美术新app
  if (ua.indexOf('bearmeishu') > -1) {
    infoMatch = ua.match(/bearmeishu.[^(]+/gi)
  }

  const infoArr = infoMatch ? infoMatch[0].split('/') : [] // ['artaiclass', '2.8.6', '10120', '38', 'zh_cn', 'cn_area', '2169']
  const version = infoArr.length ? infoArr[1] : ''
  const appChannel = infoArr.length ? infoArr[3] : ''
  // app 语言环境 ZH_CN 中文 EN_US 英文 默认中文
  const appLanguage = infoArr.length ? infoArr[4] || 'ZH_CN' : 'ZH_CN'
  // 用户手机号地区 CN_AREA 国内地区 FOREIGN_AREA  国外地区 GAT_AREA 港澳台地区  默认国内地区
  const areaType = infoArr.length ? infoArr[5] || 'CN_AREA' : 'CN_AREA'
  // 原写字appchannel 目前仅作标记，不使用 20210713
  // const writingAppChannel = infoArr.length ? infoArr[6] : undefined
  // 新增舞蹈appChannel 仅作标记位 不使用
  // const dancingAppChannel = infoArr.length ? infoArr[7] : undefined
  return {
    version,
    versionNumber: versionToNumber(version),
    appChannel,
    appLanguage: appLanguage.toLocaleUpperCase(),
    areaType: areaType.toLocaleUpperCase()
  }
})()

/**
 * 该实例返回当前页面所在的环境
 * @example: 'wx','ios','android','qq','weibo'
 * @description: new JudgeAgent().pageEnv
 */
export class JudgeAgent {
  pageEnv: string
  versions: any
  constructor() {
    this.pageEnv = ''
    this.versions = {}
    this.judgeEnv()
  }
  getVersion() {
    const u = navigator.userAgent
    return {
      // 移动终端浏览器版本信息
      trident: u.indexOf('Trident') > -1, //IE内核
      presto: u.indexOf('Presto') > -1, //opera内核
      webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
      gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
      mobile: !!u.match(/AppleWebKit.*Mobile.*/) || u.indexOf('miaobiai') > -1 || u.indexOf('BearMeishu') > -1, //是否为移动终端 (BearMeishu 为小熊美术新app)
      ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
      android: u.indexOf('Android') > -1 || u.indexOf('android') > -1 || u.indexOf('Linux') > -1, //android终端或uc浏览器
      iPhone: u.indexOf('iPhone') > -1 || u.indexOf('iphone') > -1, //是否为iPhone或者QQHD浏览器
      iPad: u.indexOf('iPad') > -1, //是否iPad
      webApp: u.indexOf('Safari') == -1, //是否web应该程序，没有头部与底部
      isTablet: /(?:iPad|PlayBook)/.test(u) || (/(?:Android)/.test(u) && !/(?:Mobile)/.test(u)) || (/(?:Firefox)/.test(u) && /(?:Tablet)/.test(u)) //是否是pad
    }
  }
  judgeEnv() {
    const versions = this.getVersion()
    this.versions = versions
    if (versions.mobile) {
      var ua = navigator.userAgent.toLowerCase() //获取判断用的对象
      if (ua.indexOf('miaobiai') > -1 || ua.indexOf('bearmeishu') > -1) {
        this.pageEnv = 'app'
      } else if (ua.indexOf('micromessenger') > -1) {
        this.pageEnv = 'wx'
      } else if (versions.ios) {
        // 是否在IOS浏览器打开
        this.pageEnv = 'ios'
      } else if (versions.android) {
        // 是否在安卓浏览器打开
        this.pageEnv = 'android'
      }
      // if (ua.match(/WeiBo/i) == "weibo") { // 在新浪微博客户端打开
      //   this.pageEnv = 'weibo'
      // }
      // if (ua.match(/QQ/i) == "qq") { // 在QQ空间打开
      //   this.pageEnv = 'qq'
      // }
      // if (versions.ios) { // 是否在IOS浏览器打开
      //   this.pageEnv = 'ios'
      // }
      // if(versions.android){ // 是否在安卓浏览器打开
      //   this.pageEnv = 'android'
      // }
    } else {
      this.pageEnv = 'pc'
    }
  }
}

// 获取url参数
export const getQuery = (name: string) => {
  const search = window.location.href.split('?')[1]
  if (!search) return ''
  return (
    search
      .split('&')
      .map((item) => item.split('='))
      .reduce((acc, [key, value]) => {
        acc[key] = value
        return acc
      }, {})[name] || ''
  )
}
