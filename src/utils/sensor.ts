import { Track } from 'msb-public-library'
import { getUser, JudgeAgent, versionInfo } from '.'

var { pageEnv } = new JudgeAgent()
const processEnv = process.env.TARO_APP_ENV || 'test'
let envMap = {
  dev: 'test',
  test: 'test',
  gray: 'gray',
  live: 'live'
}

let sensors: Track | null = null
const getUuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    let r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const takeSensors = async (event, data, options) => {
  if (!sensors) {
    console.log('sensors init...')
    console.log('window.returnCitySN--', window.returnCitySN)
    sensors = new Track({
      env: envMap[processEnv],
      // eslint-disable-next-line no-undef
      ip: window.returnCitySN ? window.returnCitySN['cip'] : '',
      user_id: options.user_id,
      app_version: options.app_version,
      business_name: options.business_name,
      showLog: processEnv !== 'live'
    })
  }
  sensors &&
    sensors
      .buried({
        event,
        app_name: options.app_name,
        properties: data,
        user_id: options.user_id
      })
      .then((res) => {
        options.callback(res)
      })
}
const bearSensorsSdk = (eventName, data = { business_name: '小熊美术' }, callback) => {
  const { business_name, ...rest } = data
  const user = getUser()
  let userId = user.id || 0
  takeSensors(eventName, rest, {
    user_id: userId,
    app_version: pageEnv === 'app' ? versionInfo.version : '',
    business_name,
    app_name: '小熊美术',
    callback
  })
}
const bearSensors = (eventName, data = {}, callback = () => {}) => {
  if (!eventName) {
    return
  }
  const _data: any = { report_view: getUuid(), ...data }
  console.log(_data)
  bearSensorsSdk(eventName, _data, callback)
}

export default bearSensors
